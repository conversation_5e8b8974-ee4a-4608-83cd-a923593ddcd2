@echo off
echo Simple GateSentinel C Beacon Build
echo ==================================

REM 创建输出目录
if not exist "bin" mkdir bin

REM 尝试使用不同的编译器
echo [INFO] Trying to compile with available compiler...

REM 方法1: 尝试使用cl (Visual Studio)
where cl >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo [INFO] Using Visual Studio compiler (cl)
    cl /nologo /O2 /MT /DWIN32 /D_WIN32 /D_CRT_SECURE_NO_WARNINGS beacon.c http.c crypto.c /Fe:bin\beacon.exe /link wininet.lib kernel32.lib
    goto check_result
)

REM 方法2: 尝试使用gcc (MinGW)
where gcc >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo [INFO] Using GCC compiler (MinGW)
    gcc -O2 -DWIN32 -D_WIN32 beacon.c http.c crypto.c -o bin\beacon.exe -lwininet -lkernel32
    goto check_result
)

REM 方法3: 尝试使用clang
where clang >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo [INFO] Using Clang compiler
    clang -O2 -DWIN32 -D_WIN32 beacon.c http.c crypto.c -o bin\beacon.exe -lwininet -lkernel32
    goto check_result
)

echo [ERROR] No suitable compiler found!
echo.
echo Please install one of the following:
echo 1. Visual Studio Community (recommended)
echo 2. Visual Studio Build Tools
echo 3. MinGW-w64
echo 4. LLVM/Clang
echo.
echo For Visual Studio, run this script from:
echo - Developer Command Prompt for VS
echo - Developer PowerShell for VS
pause
exit /b 1

:check_result
if %ERRORLEVEL% EQU 0 (
    echo.
    echo [SUCCESS] Build completed!
    echo Output: bin\beacon.exe
    if exist "bin\beacon.exe" (
        echo File size: 
        dir bin\beacon.exe | findstr "beacon.exe"
    )
    echo.
    echo Usage: bin\beacon.exe
) else (
    echo.
    echo [ERROR] Build failed!
    echo.
    echo If using Visual Studio, make sure you run from:
    echo "Developer Command Prompt for VS" or "Developer PowerShell for VS"
)

REM 清理临时文件
del *.obj >nul 2>nul

echo.
pause
