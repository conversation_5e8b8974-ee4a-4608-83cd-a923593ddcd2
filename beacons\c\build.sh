#!/bin/bash

echo "Building GateSentinel C Beacon..."

# 检查编译器
if ! command -v gcc &> /dev/null; then
    echo "Error: GCC compiler not found. Please install gcc."
    exit 1
fi

# 检查libcurl
if ! pkg-config --exists libcurl; then
    echo "Error: libcurl development package not found."
    echo "Please install libcurl-dev (Ubuntu/Debian) or libcurl-devel (CentOS/RHEL)"
    exit 1
fi

# 创建输出目录
mkdir -p bin

# 编译Beacon
echo "Compiling beacon.c..."
gcc -O2 -Wall -Wextra -std=c99 \
    beacon.c http.c \
    $(pkg-config --cflags --libs libcurl) \
    -o bin/beacon

if [ $? -eq 0 ]; then
    echo "Build successful! Output: bin/beacon"
    echo ""
    echo "Usage: ./bin/beacon"
    echo ""
    
    # 设置执行权限
    chmod +x bin/beacon
else
    echo "Build failed!"
    exit 1
fi

echo "Done."
