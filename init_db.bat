@echo off
echo ========================================
echo   GateSentinel 数据库初始化工具
echo ========================================
echo.

REM 检查SQLite3
where sqlite3 >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [WARNING] SQLite3命令行工具未找到
    echo 将使用Go程序初始化数据库...
    echo.
    goto go_init
)

REM 使用SQLite3初始化
echo [INFO] 使用SQLite3初始化数据库...
if not exist "db" mkdir db

sqlite3 db\gatesentinel.db < db\init.sql

if %ERRORLEVEL% EQU 0 (
    echo [SUCCESS] 数据库初始化成功！
    echo.
    echo 默认用户账户：
    echo   管理员: admin / admin123
    echo   操作员: operator / operator123
    echo.
    goto end
) else (
    echo [ERROR] SQLite3初始化失败，尝试Go程序初始化...
    goto go_init
)

:go_init
echo [INFO] 使用Go程序初始化数据库...
cd backend
go run -tags="init_db" main.go init
cd ..

:end
echo 按任意键退出...
pause >nul
