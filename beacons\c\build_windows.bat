@echo off
echo GateSentinel Windows C Beacon Builder
echo =====================================

REM 创建输出目录
if not exist "bin" mkdir bin

echo [INFO] Building simple Windows beacon...

REM 尝试编译简化版本
where cl >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo [INFO] Using Visual Studio compiler
    cl /nologo /O2 /MT /DWIN32 /D_WIN32 /D_CRT_SECURE_NO_WARNINGS ^
       beacon_simple.c ^
       /Fe:bin\beacon_simple.exe ^
       /link wininet.lib kernel32.lib user32.lib advapi32.lib

    if %ERRORLEVEL% EQU 0 (
        if exist "bin\beacon_simple.exe" (
            echo.
            echo [SUCCESS] Simple beacon compiled successfully!
            echo Output: bin\beacon_simple.exe
            dir bin\beacon_simple.exe | findstr "beacon_simple.exe"
            echo.
            echo Usage:
            echo   bin\beacon_simple.exe
            echo.
            echo Environment variables:
            echo   set BEACON_SERVER_URL=http://your-server:8080
            echo.
            goto cleanup
        ) else (
            echo.
            echo [ERROR] Compilation failed - executable not created
            goto try_mingw
        )
    ) else (
        echo.
        echo [ERROR] Visual Studio compilation failed
        goto try_mingw
    )
)

:try_mingw
REM 如果Visual Studio失败，尝试MinGW
where gcc >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo [INFO] Trying with MinGW GCC
    gcc -O2 -DWIN32 -D_WIN32 beacon_simple.c -o bin\beacon_simple.exe -lwininet -lkernel32
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo [SUCCESS] Simple beacon compiled with GCC!
        echo Output: bin\beacon_simple.exe
        goto cleanup
    )
)

echo.
echo [ERROR] Compilation failed!
echo.
echo Solutions:
echo 1. For Visual Studio:
echo    - Open "Developer Command Prompt for VS"
echo    - Navigate to this directory
echo    - Run this script again
echo.
echo 2. For MinGW:
echo    - Install MinGW-w64
echo    - Add to PATH
echo    - Run this script again
echo.
echo 3. Alternative: Use the C# version instead
echo    cd ..\csharp
echo    build.bat
pause
exit /b 1

:cleanup
REM 清理临时文件
del *.obj >nul 2>nul

echo Done!
pause
