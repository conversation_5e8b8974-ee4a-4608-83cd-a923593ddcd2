<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>命令执行 - GateSentinel</title>
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <div class="logo">GateSentinel</div>
            <ul class="nav-links">
                <li><a href="/">仪表板</a></li>
                <li><a href="/beacons">Beacon管理</a></li>
                <li><a href="/commands" class="active">命令执行</a></li>
                <li><a href="/shellcodes">Shellcode管理</a></li>
            </ul>
            <div class="user-info">
                <span>欢迎，<span class="user-name">用户</span> (<span class="user-role">角色</span>)</span>
                <button class="btn btn-secondary btn-sm logout-btn">登出</button>
            </div>
        </div>
    </nav>

    <!-- 主内容 -->
    <div class="container">
        <!-- 页面标题 -->
        <div class="card">
            <div class="card-header">
                <h1 class="card-title">命令执行</h1>
                <div>
                    <button class="btn btn-primary btn-sm" onclick="refreshCommands()">刷新</button>
                    <button class="btn btn-secondary btn-sm" onclick="clearResults()">清空结果</button>
                </div>
            </div>
        </div>

        <!-- Beacon选择 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">选择目标Beacon</h2>
            </div>
            <div class="form-group">
                <label class="form-label">目标Beacon</label>
                <select id="targetBeacon" class="form-input" onchange="selectBeacon()">
                    <option value="">请选择Beacon...</option>
                </select>
            </div>
            <div id="selectedBeaconInfo" style="display: none;">
                <div class="beacon-info">
                    <div class="info-item">
                        <strong>主机名:</strong> <span id="beaconHostname">-</span>
                    </div>
                    <div class="info-item">
                        <strong>用户:</strong> <span id="beaconUsername">-</span>
                    </div>
                    <div class="info-item">
                        <strong>操作系统:</strong> <span id="beaconOS">-</span>
                    </div>
                    <div class="info-item">
                        <strong>状态:</strong> <span id="beaconStatus">-</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 命令执行 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">执行命令</h2>
            </div>
            <div class="form-group">
                <label class="form-label">命令类型</label>
                <select id="commandType" class="form-input">
                    <option value="cmd">CMD (Windows)</option>
                    <option value="shell">Shell (Linux/Unix)</option>
                    <option value="powershell">PowerShell</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">命令内容</label>
                <textarea id="commandText" class="form-input" rows="3" placeholder="输入要执行的命令..."></textarea>
            </div>
            <div class="form-group">
                <button class="btn btn-primary" onclick="executeCommand()" id="executeBtn">
                    <span id="executeText">执行命令</span>
                    <span id="executeLoading" class="loading" style="display: none;"></span>
                </button>
                <button class="btn btn-secondary" onclick="clearCommand()">清空</button>
            </div>
        </div>

        <!-- 常用命令模板 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">常用命令模板</h2>
            </div>
            <div class="command-templates">
                <div class="template-category">
                    <h4>系统信息</h4>
                    <button class="btn btn-sm" onclick="useTemplate('whoami')">whoami</button>
                    <button class="btn btn-sm" onclick="useTemplate('hostname')">hostname</button>
                    <button class="btn btn-sm" onclick="useTemplate('systeminfo')">systeminfo</button>
                    <button class="btn btn-sm" onclick="useTemplate('uname -a')">uname -a</button>
                </div>
                <div class="template-category">
                    <h4>网络信息</h4>
                    <button class="btn btn-sm" onclick="useTemplate('ipconfig /all')">ipconfig</button>
                    <button class="btn btn-sm" onclick="useTemplate('ifconfig')">ifconfig</button>
                    <button class="btn btn-sm" onclick="useTemplate('netstat -an')">netstat</button>
                    <button class="btn btn-sm" onclick="useTemplate('arp -a')">arp -a</button>
                </div>
                <div class="template-category">
                    <h4>文件操作</h4>
                    <button class="btn btn-sm" onclick="useTemplate('dir')">dir</button>
                    <button class="btn btn-sm" onclick="useTemplate('ls -la')">ls -la</button>
                    <button class="btn btn-sm" onclick="useTemplate('pwd')">pwd</button>
                    <button class="btn btn-sm" onclick="useTemplate('ps aux')">ps aux</button>
                </div>
            </div>
        </div>

        <!-- 命令历史和结果 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">命令历史和结果</h2>
            </div>
            <div id="commandHistory">
                <div style="text-align: center; padding: 2rem; color: #666;">
                    暂无命令历史
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/utils.js"></script>
    <script src="/static/js/commands.js"></script>
</body>
</html>
