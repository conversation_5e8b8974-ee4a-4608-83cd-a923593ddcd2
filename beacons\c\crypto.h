#ifndef CRYPTO_H
#define CRYPTO_H

#include <stddef.h>

// 加密方法
#define CRYPTO_METHOD_NONE "none"
#define CRYPTO_METHOD_XOR "xor"
#define CRYPTO_METHOD_BASE64 "base64"

// 函数声明
int xor_encrypt_decrypt(const char *data, size_t data_len, const char *key, size_t key_len, char **output);
int base64_encode(const char *data, size_t data_len, char **output);
int base64_decode(const char *data, size_t data_len, char **output, size_t *output_len);
int encrypt_data(const char *data, size_t data_len, const char *method, const char *key, char **output);
int decrypt_data(const char *data, size_t data_len, const char *method, const char *key, char **output, size_t *output_len);
int encrypt_json(const char *json_data, const char *method, const char *key, char **output);
int decrypt_json(const char *encoded_data, const char *method, const char *key, char **output);

#endif // CRYPTO_H
