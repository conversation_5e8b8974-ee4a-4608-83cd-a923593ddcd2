<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shellcode管理 - GateSentinel</title>
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <div class="logo">GateSentinel</div>
            <ul class="nav-links">
                <li><a href="/">仪表板</a></li>
                <li><a href="/beacons">Beacon管理</a></li>
                <li><a href="/commands">命令执行</a></li>
                <li><a href="/shellcodes" class="active">Shellcode管理</a></li>
            </ul>
            <div class="user-info">
                <span>欢迎，<span class="user-name">用户</span> (<span class="user-role">角色</span>)</span>
                <button class="btn btn-secondary btn-sm logout-btn">登出</button>
            </div>
        </div>
    </nav>

    <!-- 主内容 -->
    <div class="container">
        <!-- 页面标题 -->
        <div class="card">
            <div class="card-header">
                <h1 class="card-title">Shellcode管理</h1>
                <div>
                    <button class="btn btn-primary btn-sm" onclick="showUploadModal()">上传Shellcode</button>
                    <button class="btn btn-secondary btn-sm" onclick="refreshShellcodes()">刷新</button>
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="dashboard-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalShellcodes">-</div>
                <div class="stat-label">总Shellcode数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="x86Shellcodes">-</div>
                <div class="stat-label">x86架构</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="x64Shellcodes">-</div>
                <div class="stat-label">x64架构</div>
            </div>
        </div>

        <!-- Shellcode列表 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">Shellcode列表</h2>
                <div>
                    <select id="archFilter" onchange="filterShellcodes()">
                        <option value="">所有架构</option>
                        <option value="x86">x86</option>
                        <option value="x64">x64</option>
                    </select>
                </div>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>名称</th>
                            <th>描述</th>
                            <th>架构</th>
                            <th>大小</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="shellcodesTable">
                        <tr>
                            <td colspan="6" style="text-align: center; padding: 2rem;">
                                <div class="loading"></div>
                                <span style="margin-left: 1rem;">加载中...</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 上传模态框 -->
        <div id="uploadModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>上传Shellcode</h3>
                    <span class="close" onclick="closeUploadModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label class="form-label">名称</label>
                        <input type="text" id="shellcodeName" class="form-input" placeholder="输入Shellcode名称">
                    </div>
                    <div class="form-group">
                        <label class="form-label">描述</label>
                        <textarea id="shellcodeDescription" class="form-input" rows="3" placeholder="输入Shellcode描述"></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">架构</label>
                        <select id="shellcodeArch" class="form-input">
                            <option value="x86">x86</option>
                            <option value="x64">x64</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Shellcode文件</label>
                        <input type="file" id="shellcodeFile" class="form-input" accept=".bin,.raw,.sc">
                    </div>
                    <div class="form-group">
                        <label class="form-label">或直接输入十六进制</label>
                        <textarea id="shellcodeHex" class="form-input" rows="5" placeholder="输入十六进制格式的Shellcode (如: 4831c0...)"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeUploadModal()">取消</button>
                    <button class="btn btn-primary" onclick="uploadShellcode()">上传</button>
                </div>
            </div>
        </div>

        <!-- 详情模态框 -->
        <div id="detailModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Shellcode详情</h3>
                    <span class="close" onclick="closeDetailModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="shellcodeDetails"></div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeDetailModal()">关闭</button>
                    <button class="btn btn-primary" onclick="executeShellcode()">执行</button>
                </div>
            </div>
        </div>

        <!-- 执行模态框 -->
        <div id="executeModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>执行Shellcode</h3>
                    <span class="close" onclick="closeExecuteModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label class="form-label">目标Beacon</label>
                        <select id="targetBeaconForShellcode" class="form-input">
                            <option value="">请选择Beacon...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">执行方法</label>
                        <select id="executeMethod" class="form-input">
                            <option value="inject">进程注入</option>
                            <option value="hollow">进程镂空</option>
                            <option value="direct">直接执行</option>
                        </select>
                    </div>
                    <div id="shellcodeToExecute"></div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeExecuteModal()">取消</button>
                    <button class="btn btn-danger" onclick="confirmExecuteShellcode()">确认执行</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/utils.js"></script>
    <script src="/static/js/shellcodes.js"></script>
</body>
</html>
