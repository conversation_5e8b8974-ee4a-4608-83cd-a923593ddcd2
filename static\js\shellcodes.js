// Shellcode管理页面JavaScript

let allShellcodes = [];
let currentShellcode = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async () => {
    await loadShellcodes();
    await loadBeaconsForShellcode();
});

// 加载Shellcode列表
async function loadShellcodes() {
    try {
        // 模拟Shellcode数据，因为后端API还没实现
        allShellcodes = [
            {
                id: 1,
                name: "Windows MessageBox",
                description: "显示消息框的简单Shellcode",
                arch: "x86",
                size: 256,
                created_at: new Date().toISOString(),
                hex_data: "31c050682e657865684e6f746550684d657373616765426f7850..."
            },
            {
                id: 2,
                name: "Reverse Shell",
                description: "反向Shell连接Shellcode",
                arch: "x64",
                size: 512,
                created_at: new Date().toISOString(),
                hex_data: "4831c04831db4831c94831d24831f64831ff4831ed4831f6..."
            }
        ];

        displayShellcodes(allShellcodes);
        updateStats();
    } catch (error) {
        console.error('加载Shellcode列表失败:', error);
        showError('加载Shellcode列表失败: ' + error.message);
    }
}

// 显示Shellcode列表
function displayShellcodes(shellcodes) {
    const tbody = document.getElementById('shellcodesTable');

    if (shellcodes.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" style="text-align: center; padding: 2rem; color: #666;">
                    暂无Shellcode数据
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = shellcodes.map(shellcode => `
        <tr>
            <td>${shellcode.name}</td>
            <td>${shellcode.description}</td>
            <td>
                <span class="badge ${shellcode.arch === 'x64' ? 'badge-primary' : 'badge-secondary'}">
                    ${shellcode.arch}
                </span>
            </td>
            <td>${shellcode.size} bytes</td>
            <td>${formatDateTime(shellcode.created_at)}</td>
            <td>
                <button class="btn btn-primary btn-sm" onclick="viewShellcode(${shellcode.id})">
                    查看
                </button>
                <button class="btn btn-success btn-sm" onclick="prepareExecute(${shellcode.id})">
                    执行
                </button>
                <button class="btn btn-danger btn-sm" onclick="deleteShellcode(${shellcode.id})">
                    删除
                </button>
            </td>
        </tr>
    `).join('');
}

// 更新统计信息
function updateStats() {
    const total = allShellcodes.length;
    const x86Count = allShellcodes.filter(s => s.arch === 'x86').length;
    const x64Count = allShellcodes.filter(s => s.arch === 'x64').length;

    document.getElementById('totalShellcodes').textContent = total;
    document.getElementById('x86Shellcodes').textContent = x86Count;
    document.getElementById('x64Shellcodes').textContent = x64Count;
}

// 过滤Shellcode
function filterShellcodes() {
    const archFilter = document.getElementById('archFilter').value;

    if (archFilter) {
        const filtered = allShellcodes.filter(s => s.arch === archFilter);
        displayShellcodes(filtered);
    } else {
        displayShellcodes(allShellcodes);
    }
}

// 显示上传模态框
function showUploadModal() {
    document.getElementById('uploadModal').style.display = 'block';
}

// 关闭上传模态框
function closeUploadModal() {
    document.getElementById('uploadModal').style.display = 'none';
    // 清空表单
    document.getElementById('shellcodeName').value = '';
    document.getElementById('shellcodeDescription').value = '';
    document.getElementById('shellcodeFile').value = '';
    document.getElementById('shellcodeHex').value = '';
}

// 上传Shellcode
async function uploadShellcode() {
    const name = document.getElementById('shellcodeName').value.trim();
    const description = document.getElementById('shellcodeDescription').value.trim();
    const arch = document.getElementById('shellcodeArch').value;
    const file = document.getElementById('shellcodeFile').files[0];
    const hexData = document.getElementById('shellcodeHex').value.trim();

    if (!name) {
        showError('请输入Shellcode名称');
        return;
    }

    if (!file && !hexData) {
        showError('请选择文件或输入十六进制数据');
        return;
    }

    try {
        // 模拟上传成功
        const newShellcode = {
            id: Date.now(),
            name: name,
            description: description || '无描述',
            arch: arch,
            size: hexData ? hexData.length / 2 : (file ? file.size : 0),
            created_at: new Date().toISOString(),
            hex_data: hexData || '模拟数据...'
        };

        allShellcodes.unshift(newShellcode);
        displayShellcodes(allShellcodes);
        updateStats();
        closeUploadModal();
        showSuccess('Shellcode上传成功');
    } catch (error) {
        showError('上传Shellcode失败: ' + error.message);
    }
}

// 查看Shellcode详情
function viewShellcode(id) {
    const shellcode = allShellcodes.find(s => s.id === id);
    if (!shellcode) return;

    currentShellcode = shellcode;

    const details = `
        <div class="shellcode-details">
            <div class="detail-row">
                <strong>名称:</strong> ${shellcode.name}
            </div>
            <div class="detail-row">
                <strong>描述:</strong> ${shellcode.description}
            </div>
            <div class="detail-row">
                <strong>架构:</strong> ${shellcode.arch}
            </div>
            <div class="detail-row">
                <strong>大小:</strong> ${shellcode.size} bytes
            </div>
            <div class="detail-row">
                <strong>创建时间:</strong> ${formatDateTime(shellcode.created_at)}
            </div>
            <div class="detail-row">
                <strong>十六进制数据:</strong>
                <div class="hex-data">
                    <pre>${shellcode.hex_data}</pre>
                </div>
            </div>
        </div>
    `;

    document.getElementById('shellcodeDetails').innerHTML = details;
    document.getElementById('detailModal').style.display = 'block';
}

// 准备执行Shellcode
function prepareExecute(id) {
    const shellcode = allShellcodes.find(s => s.id === id);
    if (!shellcode) return;

    currentShellcode = shellcode;

    const info = `
        <div class="execute-info">
            <div class="detail-row">
                <strong>Shellcode:</strong> ${shellcode.name}
            </div>
            <div class="detail-row">
                <strong>架构:</strong> ${shellcode.arch}
            </div>
            <div class="detail-row">
                <strong>大小:</strong> ${shellcode.size} bytes
            </div>
        </div>
    `;

    document.getElementById('shellcodeToExecute').innerHTML = info;
    document.getElementById('executeModal').style.display = 'block';
}

// 加载Beacon列表用于Shellcode执行
async function loadBeaconsForShellcode() {
    try {
        const response = await apiGet('/beacons?online=true');
        const beacons = response.beacons || [];

        const select = document.getElementById('targetBeaconForShellcode');
        select.innerHTML = '<option value="">请选择Beacon...</option>';

        beacons.forEach(beacon => {
            const option = document.createElement('option');
            option.value = beacon.beacon_id;
            option.textContent = `${beacon.hostname} (${beacon.username}) - ${beacon.beacon_id.substring(0, 8)}...`;
            select.appendChild(option);
        });

        if (beacons.length === 0) {
            select.innerHTML = '<option value="">暂无在线Beacon</option>';
        }
    } catch (error) {
        console.error('加载Beacon列表失败:', error);
    }
}

// 确认执行Shellcode
async function confirmExecuteShellcode() {
    const targetBeacon = document.getElementById('targetBeaconForShellcode').value;
    const executeMethod = document.getElementById('executeMethod').value;

    if (!targetBeacon) {
        showError('请选择目标Beacon');
        return;
    }

    if (!confirm('确定要执行这个Shellcode吗？这可能会对目标系统造成影响。')) {
        return;
    }

    try {
        // 模拟执行
        showInfo('Shellcode执行命令已发送，请等待结果...');
        closeExecuteModal();

        setTimeout(() => {
            showSuccess('Shellcode执行完成');
        }, 3000);
    } catch (error) {
        showError('执行Shellcode失败: ' + error.message);
    }
}

// 删除Shellcode
async function deleteShellcode(id) {
    if (!confirm('确定要删除这个Shellcode吗？此操作不可恢复。')) {
        return;
    }

    try {
        allShellcodes = allShellcodes.filter(s => s.id !== id);
        displayShellcodes(allShellcodes);
        updateStats();
        showSuccess('Shellcode已删除');
    } catch (error) {
        showError('删除Shellcode失败: ' + error.message);
    }
}

// 刷新Shellcode列表
async function refreshShellcodes() {
    await loadShellcodes();
    showSuccess('Shellcode列表已刷新');
}

// 关闭模态框函数
function closeDetailModal() {
    document.getElementById('detailModal').style.display = 'none';
    currentShellcode = null;
}

function closeExecuteModal() {
    document.getElementById('executeModal').style.display = 'none';
    currentShellcode = null;
}

function executeShellcode() {
    if (currentShellcode) {
        prepareExecute(currentShellcode.id);
        closeDetailModal();
    }
}