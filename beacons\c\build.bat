@echo off
echo Building GateSentinel C Beacon...

REM 检查是否有Visual Studio环境
where cl >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Error: Visual Studio compiler not found.
    echo.
    echo Please run this script from one of the following:
    echo 1. Visual Studio Developer Command Prompt
    echo 2. Visual Studio Developer PowerShell
    echo 3. After running vcvarsall.bat
    echo.
    echo Alternative: Install Visual Studio Build Tools or Visual Studio Community
    pause
    exit /b 1
)

REM 显示编译器信息
echo Using compiler:
cl 2>&1 | findstr "Version"

REM 创建输出目录
if not exist "bin" mkdir bin

REM 编译Beacon（添加更多编译选项）
echo.
echo Compiling beacon.c and http.c...
cl /nologo /O2 /MT /DWIN32 /D_WIN32 /D_CRT_SECURE_NO_WARNINGS ^
   beacon.c http.c crypto.c ^
   /Fe:bin\beacon.exe ^
   /link wininet.lib kernel32.lib user32.lib

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo Build successful!
    echo Output: bin\beacon.exe
    echo Size:
    dir bin\beacon.exe | findstr "beacon.exe"
    echo ========================================
    echo.
    echo Usage:
    echo   bin\beacon.exe
    echo.
    echo Environment variables (optional):
    echo   set BEACON_SERVER_URL=http://your-server:8080
    echo.
) else (
    echo.
    echo ========================================
    echo Build failed!
    echo ========================================
    echo.
    echo Common solutions:
    echo 1. Make sure you're in Visual Studio Command Prompt
    echo 2. Check if all source files exist
    echo 3. Verify Windows SDK is installed
    pause
    exit /b 1
)

REM 清理临时文件
del *.obj >nul 2>nul

echo Done.
pause
