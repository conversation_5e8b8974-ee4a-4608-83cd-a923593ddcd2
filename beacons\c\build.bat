@echo off
echo Building GateSentinel C Beacon...

REM 检查是否有Visual Studio环境
where cl >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Error: Visual Studio compiler not found. Please run from Visual Studio Command Prompt.
    pause
    exit /b 1
)

REM 创建输出目录
if not exist "bin" mkdir bin

REM 编译Beacon
echo Compiling beacon.c...
cl /nologo /O2 /MT /DWIN32 /D_WIN32 beacon.c http.c /Fe:bin\beacon.exe /link wininet.lib kernel32.lib

if %ERRORLEVEL% EQU 0 (
    echo Build successful! Output: bin\beacon.exe
    echo.
    echo Usage: bin\beacon.exe
    echo.
) else (
    echo Build failed!
    pause
    exit /b 1
)

REM 清理临时文件
del *.obj >nul 2>nul

echo Done.
pause
