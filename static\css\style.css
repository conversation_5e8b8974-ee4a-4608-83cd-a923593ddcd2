/* GateSentinel 样式文件 */

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #333;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 导航栏 */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.navbar .logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2a5298;
}

.navbar .nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.navbar .nav-links a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
}

.navbar .nav-links a:hover {
    color: #2a5298;
}

.navbar .user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.card-header {
    border-bottom: 1px solid #eee;
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2a5298;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #555;
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-input:focus {
    outline: none;
    border-color: #2a5298;
}

.form-input.error {
    border-color: #dc3545;
}

/* 按钮样式 */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #2a5298;
    color: white;
}

.btn-primary:hover {
    background: #1e3c72;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.table tbody tr:hover {
    background: #f8f9fa;
}

/* 状态指示器 */
.status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status.online {
    background: #d4edda;
    color: #155724;
}

.status.offline {
    background: #f8d7da;
    color: #721c24;
}

/* 登录页面样式 */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 2rem;
}

.login-card {
    background: white;
    border-radius: 10px;
    padding: 3rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    width: 100%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-header h1 {
    color: #2a5298;
    margin-bottom: 0.5rem;
}

.login-header p {
    color: #666;
}

/* 仪表板样式 */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #2a5298;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
    font-size: 0.875rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .navbar .container {
        flex-direction: column;
        gap: 1rem;
    }

    .navbar .nav-links {
        gap: 1rem;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .table {
        font-size: 0.875rem;
    }

    .card {
        padding: 1rem;
    }
}

/* 工具提示 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: #555;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #2a5298;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 警告和错误消息 */
.alert {
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 5px;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* 模态框样式 */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #2a5298;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #000;
}

/* Beacon详情样式 */
.beacon-details, .shellcode-details {
    display: grid;
    gap: 1rem;
}

.detail-row {
    display: flex;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.detail-row strong {
    min-width: 120px;
    color: #555;
}

.beacon-info {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 5px;
    margin-top: 1rem;
}

.info-item {
    display: inline-block;
    margin-right: 2rem;
    margin-bottom: 0.5rem;
}

/* 命令相关样式 */
.command-templates {
    display: grid;
    gap: 1.5rem;
}

.template-category h4 {
    margin-bottom: 0.5rem;
    color: #2a5298;
}

.template-category .btn {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.command-item {
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-bottom: 1rem;
    overflow: hidden;
}

.command-header {
    background: #f8f9fa;
    padding: 1rem;
    border-bottom: 1px solid #ddd;
}

.command-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.command-type {
    background: #2a5298;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: bold;
}

.command-time {
    color: #666;
    font-size: 0.875rem;
}

.command-content {
    padding: 1rem;
}

.command-text {
    margin-bottom: 1rem;
    font-family: monospace;
    background: #f8f9fa;
    padding: 0.5rem;
    border-radius: 3px;
}

.command-result pre {
    background: #000;
    color: #0f0;
    padding: 1rem;
    border-radius: 3px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

/* Shellcode相关样式 */
.hex-data pre {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 3px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    word-break: break-all;
    white-space: pre-wrap;
}

.execute-info {
    background: #fff3cd;
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 1rem;
}

/* 徽章样式 */
.badge {
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: bold;
}

.badge-primary {
    background: #2a5298;
    color: white;
}

.badge-secondary {
    background: #6c757d;
    color: white;
}
