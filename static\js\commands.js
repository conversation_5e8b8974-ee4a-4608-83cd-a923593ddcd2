// 命令执行页面JavaScript

let selectedBeaconId = null;
let commandHistory = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async () => {
    await loadBeacons();
    
    // 检查URL参数中是否指定了Beacon
    const urlParams = new URLSearchParams(window.location.search);
    const beaconParam = urlParams.get('beacon');
    if (beaconParam) {
        document.getElementById('targetBeacon').value = beaconParam;
        selectBeacon();
    }
});

// 加载可用的Beacon列表
async function loadBeacons() {
    try {
        const response = await apiGet('/beacons?online=true');
        const beacons = response.beacons || [];
        
        const select = document.getElementById('targetBeacon');
        select.innerHTML = '<option value="">请选择Beacon...</option>';
        
        beacons.forEach(beacon => {
            const option = document.createElement('option');
            option.value = beacon.beacon_id;
            option.textContent = `${beacon.hostname} (${beacon.username}) - ${beacon.beacon_id.substring(0, 8)}...`;
            option.dataset.beacon = JSON.stringify(beacon);
            select.appendChild(option);
        });
        
        if (beacons.length === 0) {
            select.innerHTML = '<option value="">暂无在线Beacon</option>';
        }
    } catch (error) {
        console.error('加载Beacon列表失败:', error);
        showError('加载Beacon列表失败: ' + error.message);
    }
}

// 选择Beacon
function selectBeacon() {
    const select = document.getElementById('targetBeacon');
    const selectedOption = select.options[select.selectedIndex];
    
    if (selectedOption.value) {
        selectedBeaconId = selectedOption.value;
        const beacon = JSON.parse(selectedOption.dataset.beacon);
        
        // 显示Beacon信息
        document.getElementById('beaconHostname').textContent = beacon.hostname || '-';
        document.getElementById('beaconUsername').textContent = beacon.username || '-';
        document.getElementById('beaconOS').textContent = beacon.os_info || '-';
        document.getElementById('beaconStatus').innerHTML = 
            `<span class="status ${beacon.is_online ? 'online' : 'offline'}">
                ${beacon.is_online ? '在线' : '离线'}
            </span>`;
        
        document.getElementById('selectedBeaconInfo').style.display = 'block';
        
        // 根据操作系统自动选择命令类型
        if (beacon.os_info && beacon.os_info.toLowerCase().includes('windows')) {
            document.getElementById('commandType').value = 'cmd';
        } else {
            document.getElementById('commandType').value = 'shell';
        }
        
        // 加载该Beacon的命令历史
        loadCommandHistory();
    } else {
        selectedBeaconId = null;
        document.getElementById('selectedBeaconInfo').style.display = 'none';
    }
}

// 使用命令模板
function useTemplate(command) {
    document.getElementById('commandText').value = command;
}

// 清空命令
function clearCommand() {
    document.getElementById('commandText').value = '';
}

// 执行命令
async function executeCommand() {
    if (!selectedBeaconId) {
        showError('请先选择目标Beacon');
        return;
    }
    
    const commandType = document.getElementById('commandType').value;
    const commandText = document.getElementById('commandText').value.trim();
    
    if (!commandText) {
        showError('请输入要执行的命令');
        return;
    }
    
    // 显示加载状态
    const executeBtn = document.getElementById('executeBtn');
    const executeText = document.getElementById('executeText');
    const executeLoading = document.getElementById('executeLoading');
    
    executeBtn.disabled = true;
    executeText.style.display = 'none';
    executeLoading.style.display = 'inline-block';
    
    try {
        // 这里应该调用命令执行API
        // 由于后端还没有实现，我们先模拟一个响应
        showInfo('命令已发送，等待执行结果...');
        
        // 模拟命令执行
        setTimeout(() => {
            const mockResult = {
                id: Date.now(),
                command_type: commandType,
                command_text: commandText,
                status: 'completed',
                result: `模拟执行结果:\n命令: ${commandText}\n类型: ${commandType}\n时间: ${new Date().toLocaleString()}`,
                created_at: new Date().toISOString()
            };
            
            addCommandToHistory(mockResult);
            showSuccess('命令执行完成');
        }, 2000);
        
    } catch (error) {
        showError('命令执行失败: ' + error.message);
    } finally {
        // 恢复按钮状态
        executeBtn.disabled = false;
        executeText.style.display = 'inline';
        executeLoading.style.display = 'none';
    }
}

// 加载命令历史
async function loadCommandHistory() {
    if (!selectedBeaconId) return;
    
    try {
        // 这里应该调用API获取命令历史
        // 暂时显示空状态
        const historyDiv = document.getElementById('commandHistory');
        historyDiv.innerHTML = `
            <div style="text-align: center; padding: 2rem; color: #666;">
                该Beacon暂无命令历史
            </div>
        `;
    } catch (error) {
        console.error('加载命令历史失败:', error);
    }
}

// 添加命令到历史记录
function addCommandToHistory(command) {
    commandHistory.unshift(command);
    displayCommandHistory();
}

// 显示命令历史
function displayCommandHistory() {
    const historyDiv = document.getElementById('commandHistory');
    
    if (commandHistory.length === 0) {
        historyDiv.innerHTML = `
            <div style="text-align: center; padding: 2rem; color: #666;">
                暂无命令历史
            </div>
        `;
        return;
    }
    
    historyDiv.innerHTML = commandHistory.map(cmd => `
        <div class="command-item">
            <div class="command-header">
                <div class="command-info">
                    <span class="command-type">${cmd.command_type.toUpperCase()}</span>
                    <span class="command-time">${formatDateTime(cmd.created_at)}</span>
                    <span class="status ${cmd.status === 'completed' ? 'online' : 'offline'}">
                        ${cmd.status === 'completed' ? '已完成' : '执行中'}
                    </span>
                </div>
            </div>
            <div class="command-content">
                <div class="command-text">
                    <strong>命令:</strong> ${cmd.command_text}
                </div>
                ${cmd.result ? `
                    <div class="command-result">
                        <strong>结果:</strong>
                        <pre>${cmd.result}</pre>
                    </div>
                ` : ''}
            </div>
        </div>
    `).join('');
}

// 刷新命令历史
async function refreshCommands() {
    await loadCommandHistory();
    showSuccess('命令历史已刷新');
}

// 清空结果
function clearResults() {
    if (confirm('确定要清空所有命令历史吗？')) {
        commandHistory = [];
        displayCommandHistory();
        showSuccess('命令历史已清空');
    }
}
