package main

import (
	"fmt"
	"log"
	"os"

	"gatesentinel/config"
	"gatesentinel/database"

	"golang.org/x/crypto/bcrypt"
)

func initDatabase() {
	fmt.Println("========================================")
	fmt.Println("  GateSentinel 数据库初始化")
	fmt.Println("========================================")

	// 加载配置
	configPath := "config.yaml"
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		configPath = "../config.yaml"
	}

	err := config.LoadConfig(configPath)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化数据库
	err = database.InitDatabase(config.GetDatabasePath())
	if err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}
	defer database.CloseDatabase()

	fmt.Println("[INFO] 数据库连接成功")

	// 创建表结构
	err = createTables()
	if err != nil {
		log.Fatalf("创建表结构失败: %v", err)
	}

	// 创建默认用户
	err = createDefaultUsers()
	if err != nil {
		log.Fatalf("创建默认用户失败: %v", err)
	}

	fmt.Println("[SUCCESS] 数据库初始化完成！")
	fmt.Println("")
	fmt.Println("默认用户账户：")
	fmt.Println("  管理员: admin / admin123")
	fmt.Println("  操作员: operator / operator123")
}

func createTables() error {
	fmt.Println("[INFO] 创建数据库表结构...")

	// 用户表
	_, err := database.DB.Exec(`
		CREATE TABLE IF NOT EXISTS users (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			username VARCHAR(50) UNIQUE NOT NULL,
			password_hash VARCHAR(255) NOT NULL,
			email VARCHAR(100),
			role VARCHAR(20) DEFAULT 'operator',
			is_active BOOLEAN DEFAULT 1,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			last_login DATETIME
		)
	`)
	if err != nil {
		return fmt.Errorf("创建用户表失败: %v", err)
	}

	// Beacon表
	_, err = database.DB.Exec(`
		CREATE TABLE IF NOT EXISTS beacons (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			beacon_id VARCHAR(64) UNIQUE NOT NULL,
			hostname VARCHAR(100),
			username VARCHAR(50),
			os_info VARCHAR(200),
			arch VARCHAR(20),
			process_name VARCHAR(100),
			process_id INTEGER,
			ip_address VARCHAR(45),
			external_ip VARCHAR(45),
			sleep_time INTEGER DEFAULT 60,
			jitter INTEGER DEFAULT 0,
			is_online BOOLEAN DEFAULT 1,
			first_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
			last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		return fmt.Errorf("创建Beacon表失败: %v", err)
	}

	// 创建索引
	_, err = database.DB.Exec(`CREATE INDEX IF NOT EXISTS idx_beacons_beacon_id ON beacons(beacon_id)`)
	if err != nil {
		return fmt.Errorf("创建索引失败: %v", err)
	}

	fmt.Println("[INFO] 表结构创建完成")
	return nil
}

func createDefaultUsers() error {
	fmt.Println("[INFO] 创建默认用户...")

	// 生成admin密码哈希
	adminHash, err := bcrypt.GenerateFromPassword([]byte("admin123"), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("生成admin密码哈希失败: %v", err)
	}

	// 生成operator密码哈希
	operatorHash, err := bcrypt.GenerateFromPassword([]byte("operator123"), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("生成operator密码哈希失败: %v", err)
	}

	// 插入admin用户
	_, err = database.DB.Exec(`
		INSERT OR REPLACE INTO users (username, password_hash, email, role, is_active)
		VALUES (?, ?, ?, ?, ?)
	`, "admin", string(adminHash), "<EMAIL>", "admin", true)
	if err != nil {
		return fmt.Errorf("创建admin用户失败: %v", err)
	}

	// 插入operator用户
	_, err = database.DB.Exec(`
		INSERT OR REPLACE INTO users (username, password_hash, email, role, is_active)
		VALUES (?, ?, ?, ?, ?)
	`, "operator", string(operatorHash), "<EMAIL>", "operator", true)
	if err != nil {
		return fmt.Errorf("创建operator用户失败: %v", err)
	}

	fmt.Println("[INFO] 默认用户创建完成")
	return nil
}

func main() {
	if len(os.Args) > 1 && os.Args[1] == "init" {
		initDatabase()
		return
	}

	// 正常启动服务器的代码在这里...
	fmt.Println("使用 'go run init_db.go init' 来初始化数据库")
}
