# GateSentinel 用户手册

## 概述

GateSentinel是一个准入控制与Beacon管理系统，提供了完整的C2（Command and Control）功能，支持远程主机管理、命令执行和文件传输等操作。

## 系统架构

```
┌─────────────────┐    HTTP/HTTPS    ┌─────────────────┐
│   Web界面       │ ◄──────────────► │   Go后端服务    │
│   (前端)        │                  │   (API服务器)   │
└─────────────────┘                  └─────────────────┘
                                              │
                                              │ SQLite
                                              ▼
                                     ┌─────────────────┐
                                     │   数据库        │
                                     │   (用户/Beacon) │
                                     └─────────────────┘
                                              ▲
                                              │ HTTP API
                                              │
                                     ┌─────────────────┐
                                     │   Beacon客户端  │
                                     │   (C/C#版本)    │
                                     └─────────────────┘
```

## 用户角色

### 管理员 (Admin)
- 完全系统访问权限
- 用户管理
- 系统配置
- 所有Beacon操作

### 操作员 (Operator)
- Beacon管理和操作
- 命令执行
- 文件传输
- 查看系统状态

### 查看者 (Viewer)
- 只读访问权限
- 查看Beacon状态
- 查看命令历史

## 功能详解

### 1. 用户认证

#### 登录
1. 访问系统URL（默认：http://localhost:8080）
2. 输入用户名和密码
3. 点击"登录"按钮

#### 默认账户
- 管理员：`admin` / `admin123`
- 操作员：`operator` / `operator123`

#### 修改密码
1. 登录后点击用户名
2. 选择"修改密码"
3. 输入旧密码和新密码
4. 确认修改

### 2. 仪表板

仪表板提供系统概览信息：
- Beacon统计（总数、在线、离线）
- 系统状态
- 最近活动的Beacon列表

### 3. Beacon管理

#### Beacon注册
Beacon客户端首次连接时会自动注册，包含以下信息：
- 主机名
- 用户名
- 操作系统信息
- 架构（x86/x64）
- 进程信息
- IP地址

#### Beacon状态
- **在线**：最近心跳时间在超时阈值内
- **离线**：超过超时阈值未收到心跳

#### Beacon操作
- **查看详情**：显示Beacon的详细信息
- **发送命令**：向Beacon发送执行命令
- **配置修改**：调整心跳间隔和抖动
- **删除Beacon**：从系统中移除Beacon记录

### 4. 命令执行

#### 支持的命令类型
- **CMD**：Windows命令提示符命令
- **Shell**：Linux/Unix Shell命令
- **PowerShell**：Windows PowerShell命令

#### 命令执行流程
1. 选择目标Beacon
2. 输入要执行的命令
3. 点击"执行"
4. 等待结果返回
5. 查看执行结果

#### 命令示例
```bash
# 系统信息
whoami
hostname
systeminfo  # Windows
uname -a     # Linux

# 文件操作
dir          # Windows
ls -la       # Linux
type file.txt    # Windows
cat file.txt     # Linux

# 网络信息
ipconfig     # Windows
ifconfig     # Linux
netstat -an
```

### 5. Beacon客户端使用

#### C版本Beacon

**编译：**
```bash
# Windows
cd beacons\c
build.bat

# Linux
cd beacons/c
chmod +x build.sh
./build.sh
```

**运行：**
```bash
# Windows
bin\beacon.exe

# Linux
./bin/beacon
```

#### C#版本Beacon

**编译：**
```bash
# Windows
cd beacons\csharp
build.bat

# Linux
cd beacons/csharp
chmod +x build.sh
./build.sh
```

**运行：**
```bash
# Windows
bin\win-x64\Beacon.exe

# Linux
./bin/linux-x64/Beacon
```

#### 环境变量配置
```bash
# 设置服务器地址
export BEACON_SERVER_URL=http://your-server:8080

# 设置心跳间隔（秒）
export BEACON_SLEEP_TIME=60

# 设置抖动百分比
export BEACON_JITTER=10
```

## 高级功能

### 1. 批量操作
- 选择多个Beacon执行相同命令
- 批量配置修改
- 批量状态检查

### 2. 命令模板
预定义常用命令模板：
- 系统信息收集
- 网络扫描
- 文件搜索
- 进程管理

### 3. 结果导出
- 导出命令执行结果
- 导出Beacon列表
- 导出系统日志

### 4. 实时监控
- Beacon状态实时更新
- 命令执行进度跟踪
- 系统资源监控

## 安全最佳实践

### 1. 访问控制
- 使用强密码
- 定期更换密码
- 限制登录IP范围
- 启用会话超时

### 2. 网络安全
- 使用HTTPS加密传输
- 配置防火墙规则
- 使用VPN访问
- 监控异常连接

### 3. 审计日志
- 启用详细日志记录
- 定期审查日志
- 监控异常活动
- 备份重要日志

### 4. 权限管理
- 最小权限原则
- 定期审查用户权限
- 及时删除无效账户
- 分离管理和操作权限

## 故障排除

### 常见问题

#### 1. Beacon无法连接
**症状**：Beacon客户端无法注册或心跳失败
**解决方案**：
- 检查网络连通性
- 确认服务器地址和端口
- 检查防火墙设置
- 验证服务器是否正常运行

#### 2. 命令执行失败
**症状**：发送的命令无响应或执行失败
**解决方案**：
- 检查Beacon是否在线
- 验证命令语法正确性
- 检查目标系统权限
- 查看错误日志

#### 3. Web界面无法访问
**症状**：浏览器无法打开管理界面
**解决方案**：
- 检查服务器是否启动
- 确认端口是否被占用
- 检查防火墙设置
- 验证配置文件正确性

#### 4. 登录失败
**症状**：无法使用正确的用户名密码登录
**解决方案**：
- 检查用户名密码是否正确
- 确认用户账户是否激活
- 检查数据库连接
- 重置用户密码

### 日志分析
```bash
# 查看服务器日志
tail -f logs/gatesentinel.log

# 搜索错误信息
grep ERROR logs/gatesentinel.log

# 查看特定Beacon的活动
grep "beacon_id" logs/gatesentinel.log
```

## 性能优化

### 1. 数据库优化
- 定期清理历史数据
- 优化查询索引
- 监控数据库大小

### 2. 网络优化
- 调整心跳间隔
- 使用连接池
- 启用数据压缩

### 3. 系统优化
- 监控内存使用
- 优化日志级别
- 配置合适的超时时间

## 扩展开发

### API接口
系统提供RESTful API接口，支持第三方集成：
- 用户认证API
- Beacon管理API
- 命令执行API
- 系统状态API

### 插件开发
支持自定义插件扩展功能：
- 命令处理插件
- 数据导出插件
- 通知插件
- 认证插件

## 技术支持

如遇到问题，请：
1. 查看本手册相关章节
2. 检查系统日志
3. 搜索已知问题
4. 提交Issue报告

---

**注意**：本系统仅用于授权的安全测试和教育目的，使用者需确保合法合规使用。
