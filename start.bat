@echo off
title GateSentinel - 准入控制与Beacon管理系统

echo ========================================
echo   GateSentinel 快速启动脚本
echo ========================================
echo.

REM 检查Go环境
where go >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Go未安装或未添加到PATH环境变量
    echo 请访问 https://golang.org/dl/ 下载安装Go
    pause
    exit /b 1
)

echo [INFO] 检测到Go版本: 
go version

REM 检查SQLite
where sqlite3 >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [WARNING] SQLite3未找到，将使用Go内置的SQLite驱动
)

echo.
echo [INFO] 准备启动GateSentinel服务器...
echo.

REM 切换到后端目录
cd /d "%~dp0backend"

REM 检查go.mod文件
if not exist "go.mod" (
    echo [ERROR] go.mod文件不存在，请确保在正确的目录中
    pause
    exit /b 1
)

REM 下载依赖
echo [INFO] 下载Go模块依赖...
go mod tidy

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] 下载依赖失败
    pause
    exit /b 1
)

REM 初始化数据库
echo [INFO] 初始化数据库...
if not exist "..\db" mkdir "..\db"

REM 启动服务器
echo [INFO] 启动GateSentinel服务器...
echo [INFO] Web界面将在 http://localhost:8080 启动
echo [INFO] 按 Ctrl+C 停止服务器
echo.
echo 默认登录账户：
echo   管理员: admin / admin123
echo   操作员: operator / operator123
echo.
echo ========================================

go run main.go ..\config.yaml

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo [ERROR] 服务器启动失败
    pause
    exit /b 1
)

pause
