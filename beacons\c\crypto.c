#include "crypto.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// XOR加密/解密
int xor_encrypt_decrypt(const char *data, size_t data_len, const char *key, size_t key_len, char **output) {
    if (!data || !key || !output || data_len == 0 || key_len == 0) {
        return -1;
    }
    
    *output = malloc(data_len + 1);
    if (!*output) {
        return -1;
    }
    
    for (size_t i = 0; i < data_len; i++) {
        (*output)[i] = data[i] ^ key[i % key_len];
    }
    (*output)[data_len] = '\0';
    
    return 0;
}

// Base64编码表
static const char base64_chars[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

// Base64编码
int base64_encode(const char *data, size_t data_len, char **output) {
    if (!data || !output || data_len == 0) {
        return -1;
    }
    
    size_t output_len = 4 * ((data_len + 2) / 3);
    *output = malloc(output_len + 1);
    if (!*output) {
        return -1;
    }
    
    size_t i, j;
    for (i = 0, j = 0; i < data_len;) {
        uint32_t octet_a = i < data_len ? (unsigned char)data[i++] : 0;
        uint32_t octet_b = i < data_len ? (unsigned char)data[i++] : 0;
        uint32_t octet_c = i < data_len ? (unsigned char)data[i++] : 0;
        
        uint32_t triple = (octet_a << 0x10) + (octet_b << 0x08) + octet_c;
        
        (*output)[j++] = base64_chars[(triple >> 3 * 6) & 0x3F];
        (*output)[j++] = base64_chars[(triple >> 2 * 6) & 0x3F];
        (*output)[j++] = base64_chars[(triple >> 1 * 6) & 0x3F];
        (*output)[j++] = base64_chars[(triple >> 0 * 6) & 0x3F];
    }
    
    // 添加填充
    for (i = 0; i < (3 - data_len % 3) % 3; i++) {
        (*output)[output_len - 1 - i] = '=';
    }
    
    (*output)[output_len] = '\0';
    return 0;
}

// Base64解码
int base64_decode(const char *data, size_t data_len, char **output, size_t *output_len) {
    if (!data || !output || !output_len || data_len == 0) {
        return -1;
    }
    
    // 创建解码表
    int decode_table[256];
    memset(decode_table, -1, sizeof(decode_table));
    
    for (int i = 0; i < 64; i++) {
        decode_table[(unsigned char)base64_chars[i]] = i;
    }
    
    // 计算输出长度
    *output_len = data_len / 4 * 3;
    if (data[data_len - 1] == '=') (*output_len)--;
    if (data[data_len - 2] == '=') (*output_len)--;
    
    *output = malloc(*output_len + 1);
    if (!*output) {
        return -1;
    }
    
    size_t i, j;
    for (i = 0, j = 0; i < data_len;) {
        uint32_t sextet_a = data[i] == '=' ? 0 & i++ : decode_table[(unsigned char)data[i++]];
        uint32_t sextet_b = data[i] == '=' ? 0 & i++ : decode_table[(unsigned char)data[i++]];
        uint32_t sextet_c = data[i] == '=' ? 0 & i++ : decode_table[(unsigned char)data[i++]];
        uint32_t sextet_d = data[i] == '=' ? 0 & i++ : decode_table[(unsigned char)data[i++]];
        
        uint32_t triple = (sextet_a << 3 * 6) + (sextet_b << 2 * 6) + (sextet_c << 1 * 6) + (sextet_d << 0 * 6);
        
        if (j < *output_len) (*output)[j++] = (triple >> 2 * 8) & 0xFF;
        if (j < *output_len) (*output)[j++] = (triple >> 1 * 8) & 0xFF;
        if (j < *output_len) (*output)[j++] = (triple >> 0 * 8) & 0xFF;
    }
    
    (*output)[*output_len] = '\0';
    return 0;
}

// 加密数据
int encrypt_data(const char *data, size_t data_len, const char *method, const char *key, char **output) {
    if (!data || !method || !output) {
        return -1;
    }
    
    if (strcmp(method, "none") == 0) {
        // 无加密
        *output = malloc(data_len + 1);
        if (!*output) return -1;
        memcpy(*output, data, data_len);
        (*output)[data_len] = '\0';
        return 0;
    }
    else if (strcmp(method, "xor") == 0) {
        // XOR加密
        if (!key) return -1;
        return xor_encrypt_decrypt(data, data_len, key, strlen(key), output);
    }
    else if (strcmp(method, "base64") == 0) {
        // Base64编码
        return base64_encode(data, data_len, output);
    }
    
    return -1; // 不支持的加密方法
}

// 解密数据
int decrypt_data(const char *data, size_t data_len, const char *method, const char *key, char **output, size_t *output_len) {
    if (!data || !method || !output || !output_len) {
        return -1;
    }
    
    if (strcmp(method, "none") == 0) {
        // 无加密
        *output = malloc(data_len + 1);
        if (!*output) return -1;
        memcpy(*output, data, data_len);
        (*output)[data_len] = '\0';
        *output_len = data_len;
        return 0;
    }
    else if (strcmp(method, "xor") == 0) {
        // XOR解密
        if (!key) return -1;
        int result = xor_encrypt_decrypt(data, data_len, key, strlen(key), output);
        if (result == 0) {
            *output_len = data_len;
        }
        return result;
    }
    else if (strcmp(method, "base64") == 0) {
        // Base64解码
        return base64_decode(data, data_len, output, output_len);
    }
    
    return -1; // 不支持的加密方法
}

// 加密JSON数据
int encrypt_json(const char *json_data, const char *method, const char *key, char **output) {
    if (!json_data || !method || !output) {
        return -1;
    }
    
    size_t json_len = strlen(json_data);
    char *encrypted = NULL;
    
    // 先加密
    int result = encrypt_data(json_data, json_len, method, key, &encrypted);
    if (result != 0) {
        return -1;
    }
    
    // 如果不是Base64方法，需要再进行Base64编码以便传输
    if (strcmp(method, "base64") != 0) {
        char *base64_encoded = NULL;
        result = base64_encode(encrypted, strlen(encrypted), &base64_encoded);
        free(encrypted);
        
        if (result != 0) {
            return -1;
        }
        
        *output = base64_encoded;
    } else {
        *output = encrypted;
    }
    
    return 0;
}

// 解密JSON数据
int decrypt_json(const char *encoded_data, const char *method, const char *key, char **output) {
    if (!encoded_data || !method || !output) {
        return -1;
    }
    
    char *decoded = NULL;
    size_t decoded_len = 0;
    
    // 如果不是Base64方法，需要先进行Base64解码
    if (strcmp(method, "base64") != 0) {
        int result = base64_decode(encoded_data, strlen(encoded_data), &decoded, &decoded_len);
        if (result != 0) {
            return -1;
        }
    } else {
        decoded = (char*)encoded_data;
        decoded_len = strlen(encoded_data);
    }
    
    // 然后解密
    size_t output_len = 0;
    int result = decrypt_data(decoded, decoded_len, method, key, output, &output_len);
    
    // 如果之前分配了内存，需要释放
    if (strcmp(method, "base64") != 0) {
        free(decoded);
    }
    
    return result;
}
