// GateSentinel Simple C Beacon for Windows
// 简化版本，专门用于Windows编译

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <windows.h>
#include <wininet.h>

#pragma comment(lib, "wininet.lib")
#pragma comment(lib, "kernel32.lib")
#pragma comment(lib, "advapi32.lib")

// 配置常量
#define DEFAULT_SERVER_URL "http://127.0.0.1:8080"
#define DEFAULT_SLEEP_TIME 60
#define MAX_BUFFER_SIZE 4096
#define MAX_BEACON_ID_LEN 64

// 全局变量
char g_beacon_id[MAX_BEACON_ID_LEN];
char g_server_url[256] = DEFAULT_SERVER_URL;
int g_sleep_time = DEFAULT_SLEEP_TIME;

// 生成随机Beacon ID
void generate_beacon_id() {
    srand((unsigned int)time(NULL));
    sprintf_s(g_beacon_id, sizeof(g_beacon_id), 
              "%08x%08x%08x%08x", rand(), rand(), rand(), rand());
}

// 获取系统信息
void get_system_info(char* hostname, char* username, char* os_info, char* arch) {
    DWORD size;
    
    // 主机名
    size = 256;
    GetComputerNameA(hostname, &size);
    
    // 用户名
    size = 256;
    GetUserNameA(username, &size);
    
    // 操作系统信息
    OSVERSIONINFOA osvi;
    ZeroMemory(&osvi, sizeof(OSVERSIONINFOA));
    osvi.dwOSVersionInfoSize = sizeof(OSVERSIONINFOA);
    
    if (GetVersionExA(&osvi)) {
        sprintf_s(os_info, 256, "Windows %d.%d Build %d", 
                 osvi.dwMajorVersion, osvi.dwMinorVersion, osvi.dwBuildNumber);
    } else {
        strcpy_s(os_info, 256, "Windows Unknown");
    }
    
    // 架构
    SYSTEM_INFO si;
    GetSystemInfo(&si);
    switch (si.wProcessorArchitecture) {
        case PROCESSOR_ARCHITECTURE_AMD64:
            strcpy_s(arch, 32, "x64");
            break;
        case PROCESSOR_ARCHITECTURE_INTEL:
            strcpy_s(arch, 32, "x86");
            break;
        default:
            strcpy_s(arch, 32, "unknown");
    }
}

// HTTP POST请求
int http_post(const char* url, const char* data, char* response, int response_size) {
    HINTERNET hInternet, hConnect, hRequest;
    DWORD bytesRead;
    char buffer[1024];
    int total_read = 0;
    
    // 初始化WinINet
    hInternet = InternetOpenA("GateSentinel-Beacon/1.0", 
                             INTERNET_OPEN_TYPE_DIRECT, NULL, NULL, 0);
    if (!hInternet) return -1;
    
    // 解析URL
    char hostname[256] = {0};
    char path[512] = {0};
    int port = 80;
    
    if (strncmp(url, "http://", 7) == 0) {
        sscanf_s(url + 7, "%255[^/]%511s", hostname, (unsigned)sizeof(hostname), path, (unsigned)sizeof(path));
    } else {
        InternetCloseHandle(hInternet);
        return -1;
    }
    
    // 检查端口
    char* portPos = strchr(hostname, ':');
    if (portPos) {
        *portPos = '\0';
        port = atoi(portPos + 1);
    }
    
    if (strlen(path) == 0) {
        strcpy_s(path, sizeof(path), "/");
    }
    
    // 连接到服务器
    hConnect = InternetConnectA(hInternet, hostname, port, NULL, NULL, 
                               INTERNET_SERVICE_HTTP, 0, 0);
    if (!hConnect) {
        InternetCloseHandle(hInternet);
        return -1;
    }
    
    // 创建HTTP请求
    hRequest = HttpOpenRequestA(hConnect, "POST", path, NULL, NULL, NULL, 
                               INTERNET_FLAG_RELOAD | INTERNET_FLAG_NO_CACHE_WRITE, 0);
    if (!hRequest) {
        InternetCloseHandle(hConnect);
        InternetCloseHandle(hInternet);
        return -1;
    }
    
    // 设置请求头
    const char* headers = "Content-Type: application/json\r\n";
    
    // 发送请求
    BOOL result = HttpSendRequestA(hRequest, headers, (DWORD)strlen(headers), 
                                  (LPVOID)data, (DWORD)strlen(data));
    if (!result) {
        InternetCloseHandle(hRequest);
        InternetCloseHandle(hConnect);
        InternetCloseHandle(hInternet);
        return -1;
    }
    
    // 读取响应
    response[0] = '\0';
    while (InternetReadFile(hRequest, buffer, sizeof(buffer) - 1, &bytesRead) && bytesRead > 0) {
        buffer[bytesRead] = '\0';
        if (total_read + bytesRead < response_size - 1) {
            strcat_s(response, response_size, buffer);
            total_read += bytesRead;
        }
    }
    
    InternetCloseHandle(hRequest);
    InternetCloseHandle(hConnect);
    InternetCloseHandle(hInternet);
    
    return 0;
}

// 注册Beacon
int register_beacon() {
    char hostname[256], username[256], os_info[256], arch[32];
    char json_data[2048];
    char response[4096];
    char url[512];
    
    printf("[INFO] 收集系统信息...\n");
    get_system_info(hostname, username, os_info, arch);
    
    // 构建注册JSON数据
    sprintf_s(json_data, sizeof(json_data),
        "{"
        "\"hostname\":\"%s\","
        "\"username\":\"%s\","
        "\"os_info\":\"%s\","
        "\"arch\":\"%s\","
        "\"process_name\":\"beacon.exe\","
        "\"process_id\":%d,"
        "\"external_ip\":\"\""
        "}",
        hostname, username, os_info, arch, GetCurrentProcessId()
    );
    
    // 发送注册请求
    sprintf_s(url, sizeof(url), "%s/api/v1/beacon/register", g_server_url);
    
    printf("[INFO] 注册到服务器: %s\n", g_server_url);
    int result = http_post(url, json_data, response, sizeof(response));
    
    if (result == 0) {
        printf("[INFO] Beacon注册成功\n");
        printf("[DEBUG] 响应: %s\n", response);
        return 0;
    } else {
        printf("[ERROR] Beacon注册失败\n");
        return -1;
    }
}

// 发送心跳
int send_heartbeat() {
    char url[512];
    char response[4096];
    
    sprintf_s(url, sizeof(url), "%s/api/v1/beacon/%s/heartbeat", g_server_url, g_beacon_id);
    
    int result = http_post(url, "{}", response, sizeof(response));
    
    if (result == 0) {
        printf("[INFO] 心跳发送成功\n");
        return 0;
    } else {
        printf("[ERROR] 心跳发送失败\n");
        return -1;
    }
}

// 主函数
int main() {
    printf("GateSentinel Simple C Beacon v1.0\n");
    printf("==================================\n");
    
    // 检查环境变量
    char* env_url = getenv("BEACON_SERVER_URL");
    if (env_url) {
        strcpy_s(g_server_url, sizeof(g_server_url), env_url);
    }
    
    // 生成Beacon ID
    generate_beacon_id();
    printf("[INFO] Beacon ID: %s\n", g_beacon_id);
    printf("[INFO] 服务器: %s\n", g_server_url);
    
    // 注册Beacon
    if (register_beacon() != 0) {
        printf("[ERROR] 注册失败，退出\n");
        system("pause");
        return 1;
    }
    
    // 主循环
    printf("[INFO] 开始心跳循环...\n");
    while (1) {
        send_heartbeat();
        
        printf("[INFO] 等待 %d 秒...\n", g_sleep_time);
        Sleep(g_sleep_time * 1000);
    }
    
    return 0;
}
