package database

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"path/filepath"

	_ "github.com/mattn/go-sqlite3"
)

// DB 全局数据库连接
var DB *sql.DB

// InitDatabase 初始化数据库连接
func InitDatabase(dbPath string) error {
	// 确保数据库目录存在
	dbDir := filepath.Dir(dbPath)
	if err := os.MkdirAll(dbDir, 0755); err != nil {
		return fmt.Errorf("创建数据库目录失败: %v", err)
	}

	// 打开数据库连接
	var err error
	DB, err = sql.Open("sqlite3", dbPath+"?_foreign_keys=on")
	if err != nil {
		return fmt.Errorf("打开数据库失败: %v", err)
	}

	// 测试连接
	if err = DB.Ping(); err != nil {
		return fmt.Errorf("数据库连接测试失败: %v", err)
	}

	// 设置连接池参数
	DB.SetMaxOpenConns(25)
	DB.SetMaxIdleConns(25)

	log.Println("数据库连接成功")
	return nil
}

// CloseDatabase 关闭数据库连接
func CloseDatabase() error {
	if DB != nil {
		return DB.Close()
	}
	return nil
}

// ExecuteSchema 执行SQL脚本文件
func ExecuteSchema(schemaPath string) error {
	// 读取SQL文件
	schemaBytes, err := os.ReadFile(schemaPath)
	if err != nil {
		return fmt.Errorf("读取SQL文件失败: %v", err)
	}

	// 执行SQL脚本
	_, err = DB.Exec(string(schemaBytes))
	if err != nil {
		return fmt.Errorf("执行SQL脚本失败: %v", err)
	}

	log.Println("数据库初始化完成")
	return nil
}

// Transaction 事务处理函数类型
type Transaction func(*sql.Tx) error

// WithTransaction 执行事务
func WithTransaction(fn Transaction) error {
	tx, err := DB.Begin()
	if err != nil {
		return err
	}

	defer func() {
		if p := recover(); p != nil {
			tx.Rollback()
			panic(p)
		} else if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit()
		}
	}()

	err = fn(tx)
	return err
}

// IsTableExists 检查表是否存在
func IsTableExists(tableName string) (bool, error) {
	query := `SELECT name FROM sqlite_master WHERE type='table' AND name=?`
	var name string
	err := DB.QueryRow(query, tableName).Scan(&name)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// InitializeTables 初始化数据库表结构
func InitializeTables() error {
	// 用户表
	_, err := DB.Exec(`
		CREATE TABLE IF NOT EXISTS users (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			username VARCHAR(50) UNIQUE NOT NULL,
			password_hash VARCHAR(255) NOT NULL,
			email VARCHAR(100),
			role VARCHAR(20) DEFAULT 'operator',
			is_active BOOLEAN DEFAULT 1,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			last_login DATETIME
		)
	`)
	if err != nil {
		return fmt.Errorf("创建用户表失败: %v", err)
	}

	// Beacon表
	_, err = DB.Exec(`
		CREATE TABLE IF NOT EXISTS beacons (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			beacon_id VARCHAR(64) UNIQUE NOT NULL,
			hostname VARCHAR(100),
			username VARCHAR(50),
			os_info VARCHAR(200),
			arch VARCHAR(20),
			process_name VARCHAR(100),
			process_id INTEGER,
			ip_address VARCHAR(45),
			external_ip VARCHAR(45),
			sleep_time INTEGER DEFAULT 60,
			jitter INTEGER DEFAULT 0,
			is_online BOOLEAN DEFAULT 1,
			first_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
			last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		return fmt.Errorf("创建Beacon表失败: %v", err)
	}

	// 命令表
	_, err = DB.Exec(`
		CREATE TABLE IF NOT EXISTS commands (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			beacon_id VARCHAR(64) NOT NULL,
			command_type VARCHAR(20) NOT NULL,
			command_text TEXT NOT NULL,
			status VARCHAR(20) DEFAULT 'pending',
			created_by INTEGER NOT NULL,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			sent_at DATETIME,
			completed_at DATETIME,
			FOREIGN KEY (beacon_id) REFERENCES beacons(beacon_id),
			FOREIGN KEY (created_by) REFERENCES users(id)
		)
	`)
	if err != nil {
		return fmt.Errorf("创建命令表失败: %v", err)
	}

	// 命令结果表
	_, err = DB.Exec(`
		CREATE TABLE IF NOT EXISTS command_results (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			command_id INTEGER NOT NULL,
			result_text TEXT,
			error_text TEXT,
			exit_code INTEGER,
			execution_time INTEGER,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (command_id) REFERENCES commands(id)
		)
	`)
	if err != nil {
		return fmt.Errorf("创建命令结果表失败: %v", err)
	}

	// 系统日志表
	_, err = DB.Exec(`
		CREATE TABLE IF NOT EXISTS system_logs (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			level VARCHAR(10) NOT NULL,
			category VARCHAR(50),
			message TEXT NOT NULL,
			user_id INTEGER,
			beacon_id VARCHAR(64),
			ip_address VARCHAR(45),
			user_agent TEXT,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (user_id) REFERENCES users(id),
			FOREIGN KEY (beacon_id) REFERENCES beacons(beacon_id)
		)
	`)
	if err != nil {
		return fmt.Errorf("创建系统日志表失败: %v", err)
	}

	// 创建索引
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_beacons_beacon_id ON beacons(beacon_id)",
		"CREATE INDEX IF NOT EXISTS idx_beacons_last_seen ON beacons(last_seen)",
		"CREATE INDEX IF NOT EXISTS idx_beacons_is_online ON beacons(is_online)",
		"CREATE INDEX IF NOT EXISTS idx_commands_beacon_id ON commands(beacon_id)",
		"CREATE INDEX IF NOT EXISTS idx_commands_status ON commands(status)",
		"CREATE INDEX IF NOT EXISTS idx_commands_created_at ON commands(created_at)",
		"CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs(created_at)",
		"CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(level)",
	}

	for _, indexSQL := range indexes {
		_, err = DB.Exec(indexSQL)
		if err != nil {
			return fmt.Errorf("创建索引失败: %v", err)
		}
	}

	log.Println("数据库表结构初始化完成")
	return nil
}

// GetDatabaseStats 获取数据库统计信息
func GetDatabaseStats() (map[string]int, error) {
	stats := make(map[string]int)

	tables := []string{"users", "beacons", "commands", "command_results", "system_logs"}

	for _, table := range tables {
		var count int
		query := fmt.Sprintf("SELECT COUNT(*) FROM %s", table)
		err := DB.QueryRow(query).Scan(&count)
		if err != nil {
			// 如果表不存在，计数为0
			stats[table] = 0
		} else {
			stats[table] = count
		}
	}

	return stats, nil
}
