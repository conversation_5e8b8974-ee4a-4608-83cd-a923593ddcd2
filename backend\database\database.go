package database

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"path/filepath"

	_ "github.com/mattn/go-sqlite3"
)

// DB 全局数据库连接
var DB *sql.DB

// InitDatabase 初始化数据库连接
func InitDatabase(dbPath string) error {
	// 确保数据库目录存在
	dbDir := filepath.Dir(dbPath)
	if err := os.MkdirAll(dbDir, 0755); err != nil {
		return fmt.Errorf("创建数据库目录失败: %v", err)
	}

	// 打开数据库连接
	var err error
	DB, err = sql.Open("sqlite3", dbPath+"?_foreign_keys=on")
	if err != nil {
		return fmt.Errorf("打开数据库失败: %v", err)
	}

	// 测试连接
	if err = DB.Ping(); err != nil {
		return fmt.Errorf("数据库连接测试失败: %v", err)
	}

	// 设置连接池参数
	DB.SetMaxOpenConns(25)
	DB.SetMaxIdleConns(25)

	log.Println("数据库连接成功")
	return nil
}

// CloseDatabase 关闭数据库连接
func CloseDatabase() error {
	if DB != nil {
		return DB.Close()
	}
	return nil
}

// ExecuteSchema 执行SQL脚本文件
func ExecuteSchema(schemaPath string) error {
	// 读取SQL文件
	schemaBytes, err := os.ReadFile(schemaPath)
	if err != nil {
		return fmt.Errorf("读取SQL文件失败: %v", err)
	}

	// 执行SQL脚本
	_, err = DB.Exec(string(schemaBytes))
	if err != nil {
		return fmt.Errorf("执行SQL脚本失败: %v", err)
	}

	log.Println("数据库初始化完成")
	return nil
}

// Transaction 事务处理函数类型
type Transaction func(*sql.Tx) error

// WithTransaction 执行事务
func WithTransaction(fn Transaction) error {
	tx, err := DB.Begin()
	if err != nil {
		return err
	}

	defer func() {
		if p := recover(); p != nil {
			tx.Rollback()
			panic(p)
		} else if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit()
		}
	}()

	err = fn(tx)
	return err
}

// IsTableExists 检查表是否存在
func IsTableExists(tableName string) (bool, error) {
	query := `SELECT name FROM sqlite_master WHERE type='table' AND name=?`
	var name string
	err := DB.QueryRow(query, tableName).Scan(&name)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// GetDatabaseStats 获取数据库统计信息
func GetDatabaseStats() (map[string]int, error) {
	stats := make(map[string]int)
	
	tables := []string{"users", "beacons", "commands", "command_results", "shellcodes", "system_logs"}
	
	for _, table := range tables {
		var count int
		query := fmt.Sprintf("SELECT COUNT(*) FROM %s", table)
		err := DB.QueryRow(query).Scan(&count)
		if err != nil {
			return nil, fmt.Errorf("查询表 %s 统计信息失败: %v", table, err)
		}
		stats[table] = count
	}
	
	return stats, nil
}
