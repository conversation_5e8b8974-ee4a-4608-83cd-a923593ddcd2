# GateSentinel 部署指南

## 系统要求

### 服务器端
- **操作系统**: Windows 10/11, Windows Server 2016+, Ubuntu 18.04+, CentOS 7+
- **Go语言**: 1.19或更高版本
- **内存**: 最少512MB，推荐1GB+
- **存储**: 最少100MB可用空间
- **网络**: 需要开放HTTP端口（默认8080）

### Beacon客户端
- **C版本**: 
  - Windows: Visual Studio 2019+ 或 MinGW-w64
  - Linux: GCC 4.8+ 和 libcurl-dev
- **C#版本**: .NET 6.0 Runtime或更高版本

## 快速部署

### 1. 下载项目
```bash
git clone <repository-url>
cd GateSentinel
```

### 2. 配置服务器
编辑 `config.yaml` 文件，修改必要的配置项：
```yaml
server:
  host: "0.0.0.0"  # 监听所有接口
  port: 8080        # HTTP端口

security:
  jwt_secret: "your-super-secret-jwt-key-change-this-in-production"
```

### 3. 启动服务器

#### Windows
```cmd
# 使用快速启动脚本
start.bat

# 或手动启动
cd backend
go mod tidy
go run main.go ..\config.yaml
```

#### Linux
```bash
# 使用快速启动脚本
chmod +x start.sh
./start.sh

# 或手动启动
cd backend
go mod tidy
go run main.go ../config.yaml
```

### 4. 访问Web界面
打开浏览器访问: `http://localhost:8080`

默认登录账户：
- 管理员: `admin` / `admin123`
- 操作员: `operator` / `operator123`

## 编译Beacon客户端

### C版本Beacon

#### Windows
```cmd
cd beacons\c
build.bat
```

#### Linux
```bash
cd beacons/c
chmod +x build.sh
./build.sh
```

### C#版本Beacon

#### Windows
```cmd
cd beacons\csharp
build.bat
```

#### Linux
```bash
cd beacons/csharp
chmod +x build.sh
./build.sh
```

## 生产环境部署

### 1. 安全配置
- 修改默认密码
- 更换JWT密钥
- 启用HTTPS（推荐使用反向代理）
- 配置防火墙规则

### 2. 数据库配置
```yaml
database:
  path: "/var/lib/gatesentinel/gatesentinel.db"  # 生产环境路径
```

### 3. 日志配置
```yaml
logging:
  level: "info"
  file: "/var/log/gatesentinel/gatesentinel.log"
  max_size: 100
  max_backups: 5
  max_age: 30
```

### 4. 系统服务配置

#### Linux Systemd服务
创建 `/etc/systemd/system/gatesentinel.service`:
```ini
[Unit]
Description=GateSentinel Beacon Management System
After=network.target

[Service]
Type=simple
User=gatesentinel
Group=gatesentinel
WorkingDirectory=/opt/gatesentinel
ExecStart=/opt/gatesentinel/backend/gatesentinel /opt/gatesentinel/config.yaml
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable gatesentinel
sudo systemctl start gatesentinel
```

#### Windows服务
使用NSSM或类似工具将程序注册为Windows服务。

### 5. 反向代理配置

#### Nginx配置示例
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 故障排除

### 常见问题

1. **端口被占用**
   - 修改 `config.yaml` 中的端口号
   - 或停止占用端口的程序

2. **数据库权限错误**
   - 确保数据库目录有写权限
   - 检查SELinux设置（Linux）

3. **Beacon连接失败**
   - 检查防火墙设置
   - 确认服务器地址配置正确
   - 检查网络连通性

4. **编译错误**
   - 确保安装了正确版本的编译器
   - 检查依赖库是否安装

### 日志查看
```bash
# 查看实时日志
tail -f /var/log/gatesentinel/gatesentinel.log

# 查看错误日志
grep ERROR /var/log/gatesentinel/gatesentinel.log
```

### 性能监控
- 监控CPU和内存使用率
- 检查数据库大小增长
- 监控网络连接数

## 备份与恢复

### 备份
```bash
# 备份数据库
cp /path/to/gatesentinel.db /backup/location/

# 备份配置文件
cp /path/to/config.yaml /backup/location/
```

### 恢复
```bash
# 恢复数据库
cp /backup/location/gatesentinel.db /path/to/

# 恢复配置
cp /backup/location/config.yaml /path/to/
```

## 更新升级

1. 停止服务
2. 备份数据库和配置
3. 更新程序文件
4. 检查配置文件兼容性
5. 启动服务
6. 验证功能正常

## 安全建议

1. 定期更新系统和依赖
2. 使用强密码和双因素认证
3. 限制网络访问范围
4. 定期审计用户权限
5. 监控异常活动
6. 定期备份重要数据
