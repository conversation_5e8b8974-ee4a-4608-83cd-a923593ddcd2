package config

import (
	"fmt"
	"os"

	"gopkg.in/yaml.v3"
)

// Config 应用配置结构
type Config struct {
	Server struct {
		Host  string `yaml:"host"`
		Port  int    `yaml:"port"`
		Debug bool   `yaml:"debug"`
	} `yaml:"server"`

	Database struct {
		Type string `yaml:"type"`
		Path string `yaml:"path"`
	} `yaml:"database"`

	Security struct {
		JWTSecret      string `yaml:"jwt_secret"`
		JWTExpireHours int    `yaml:"jwt_expire_hours"`
		BcryptCost     int    `yaml:"bcrypt_cost"`
	} `yaml:"security"`

	Beacon struct {
		DefaultSleep int `yaml:"default_sleep"`
		MaxSleep     int `yaml:"max_sleep"`
		Timeout      int `yaml:"timeout"`
	} `yaml:"beacon"`

	Encryption struct {
		Enabled bool   `yaml:"enabled"`
		Method  string `yaml:"method"`
		Key     string `yaml:"key"`
	} `yaml:"encryption"`

	Logging struct {
		Level      string `yaml:"level"`
		File       string `yaml:"file"`
		MaxSize    int    `yaml:"max_size"`
		MaxBackups int    `yaml:"max_backups"`
		MaxAge     int    `yaml:"max_age"`
	} `yaml:"logging"`

	Web struct {
		StaticPath     string `yaml:"static_path"`
		TemplatePath   string `yaml:"template_path"`
		UploadPath     string `yaml:"upload_path"`
		MaxUploadSize  int    `yaml:"max_upload_size"`
	} `yaml:"web"`
}

// AppConfig 全局配置实例
var AppConfig *Config

// LoadConfig 加载配置文件
func LoadConfig(configPath string) error {
	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析YAML配置
	AppConfig = &Config{}
	err = yaml.Unmarshal(data, AppConfig)
	if err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 验证必要的配置项
	if err := validateConfig(); err != nil {
		return fmt.Errorf("配置验证失败: %v", err)
	}

	return nil
}

// validateConfig 验证配置项
func validateConfig() error {
	if AppConfig.Server.Port <= 0 || AppConfig.Server.Port > 65535 {
		return fmt.Errorf("无效的服务器端口: %d", AppConfig.Server.Port)
	}

	if AppConfig.Security.JWTSecret == "" {
		return fmt.Errorf("JWT密钥不能为空")
	}

	if AppConfig.Security.JWTExpireHours <= 0 {
		return fmt.Errorf("JWT过期时间必须大于0")
	}

	if AppConfig.Database.Path == "" {
		return fmt.Errorf("数据库路径不能为空")
	}

	if AppConfig.Beacon.DefaultSleep <= 0 {
		return fmt.Errorf("Beacon默认休眠时间必须大于0")
	}

	if AppConfig.Beacon.Timeout <= 0 {
		return fmt.Errorf("Beacon超时时间必须大于0")
	}

	return nil
}

// GetServerAddress 获取服务器地址
func GetServerAddress() string {
	return fmt.Sprintf("%s:%d", AppConfig.Server.Host, AppConfig.Server.Port)
}

// IsDebugMode 是否为调试模式
func IsDebugMode() bool {
	return AppConfig.Server.Debug
}

// GetJWTSecret 获取JWT密钥
func GetJWTSecret() []byte {
	return []byte(AppConfig.Security.JWTSecret)
}

// GetDatabasePath 获取数据库路径
func GetDatabasePath() string {
	return AppConfig.Database.Path
}

// GetBeaconTimeout 获取Beacon超时时间
func GetBeaconTimeout() int {
	return AppConfig.Beacon.Timeout
}

// GetEncryptionConfig 获取加密配置
func GetEncryptionConfig() (bool, string, string) {
	return AppConfig.Encryption.Enabled, AppConfig.Encryption.Method, AppConfig.Encryption.Key
}

// GetUploadPath 获取上传路径
func GetUploadPath() string {
	return AppConfig.Web.UploadPath
}

// GetMaxUploadSize 获取最大上传大小（字节）
func GetMaxUploadSize() int64 {
	return int64(AppConfig.Web.MaxUploadSize) * 1024 * 1024 // 转换为字节
}
