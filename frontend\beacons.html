<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Beacon管理 - GateSentinel</title>
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <div class="logo">GateSentinel</div>
            <ul class="nav-links">
                <li><a href="/">仪表板</a></li>
                <li><a href="/beacons" class="active">Beacon管理</a></li>
                <li><a href="/commands">命令执行</a></li>
                <li><a href="/shellcodes">Shellcode管理</a></li>
            </ul>
            <div class="user-info">
                <span>欢迎，<span class="user-name">用户</span> (<span class="user-role">角色</span>)</span>
                <button class="btn btn-secondary btn-sm logout-btn">登出</button>
            </div>
        </div>
    </nav>

    <!-- 主内容 -->
    <div class="container">
        <!-- 页面标题 -->
        <div class="card">
            <div class="card-header">
                <h1 class="card-title">Beacon管理</h1>
                <div>
                    <button class="btn btn-primary btn-sm" onclick="refreshBeacons()">刷新</button>
                    <button class="btn btn-secondary btn-sm" onclick="toggleAutoRefresh()">
                        <span id="autoRefreshText">开启自动刷新</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="dashboard-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalBeacons">-</div>
                <div class="stat-label">总Beacon数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="onlineBeacons">-</div>
                <div class="stat-label">在线Beacon</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="offlineBeacons">-</div>
                <div class="stat-label">离线Beacon</div>
            </div>
        </div>

        <!-- Beacon列表 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">Beacon列表</h2>
                <div>
                    <label>
                        <input type="checkbox" id="onlineOnlyFilter" onchange="filterBeacons()"> 仅显示在线
                    </label>
                </div>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Beacon ID</th>
                            <th>主机名</th>
                            <th>用户</th>
                            <th>操作系统</th>
                            <th>架构</th>
                            <th>进程</th>
                            <th>IP地址</th>
                            <th>首次上线</th>
                            <th>最后上线</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="beaconsTable">
                        <tr>
                            <td colspan="11" style="text-align: center; padding: 2rem;">
                                <div class="loading"></div>
                                <span style="margin-left: 1rem;">加载中...</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Beacon详情模态框 -->
        <div id="beaconModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Beacon详情</h3>
                    <span class="close" onclick="closeBeaconModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="beaconDetails"></div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeBeaconModal()">关闭</button>
                    <button class="btn btn-primary" onclick="sendCommandToBeacon()">发送命令</button>
                </div>
            </div>
        </div>

        <!-- 配置模态框 -->
        <div id="configModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>配置Beacon</h3>
                    <span class="close" onclick="closeConfigModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label class="form-label">心跳间隔（秒）</label>
                        <input type="number" id="sleepTime" class="form-input" min="1" max="3600" value="60">
                    </div>
                    <div class="form-group">
                        <label class="form-label">抖动（%）</label>
                        <input type="number" id="jitter" class="form-input" min="0" max="100" value="10">
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeConfigModal()">取消</button>
                    <button class="btn btn-primary" onclick="saveBeaconConfig()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/utils.js"></script>
    <script src="/static/js/beacons.js"></script>
</body>
</html>
