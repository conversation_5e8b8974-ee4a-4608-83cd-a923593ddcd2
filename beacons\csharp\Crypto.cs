using System;
using System.Security.Cryptography;
using System.Text;

namespace GateSentinel.Beacon
{
    public enum EncryptionMethod
    {
        None,
        XOR,
        Base64,
        AES
    }

    public static class CryptoHelper
    {
        public static string EncryptData(string data, EncryptionMethod method, string key = "")
        {
            if (string.IsNullOrEmpty(data))
                return data;

            try
            {
                switch (method)
                {
                    case EncryptionMethod.None:
                        return data;

                    case EncryptionMethod.XOR:
                        return XOREncrypt(data, key);

                    case EncryptionMethod.Base64:
                        return Base64Encode(data);

                    case EncryptionMethod.AES:
                        return AESEncrypt(data, key);

                    default:
                        return data;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 加密失败: {ex.Message}");
                return data;
            }
        }

        public static string DecryptData(string data, EncryptionMethod method, string key = "")
        {
            if (string.IsNullOrEmpty(data))
                return data;

            try
            {
                switch (method)
                {
                    case EncryptionMethod.None:
                        return data;

                    case EncryptionMethod.XOR:
                        return XORDecrypt(data, key);

                    case EncryptionMethod.Base64:
                        return Base64Decode(data);

                    case EncryptionMethod.AES:
                        return AESDecrypt(data, key);

                    default:
                        return data;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 解密失败: {ex.Message}");
                return data;
            }
        }

        private static string XOREncrypt(string data, string key)
        {
            if (string.IsNullOrEmpty(key))
                return data;

            var dataBytes = Encoding.UTF8.GetBytes(data);
            var keyBytes = Encoding.UTF8.GetBytes(key);
            var result = new byte[dataBytes.Length];

            for (int i = 0; i < dataBytes.Length; i++)
            {
                result[i] = (byte)(dataBytes[i] ^ keyBytes[i % keyBytes.Length]);
            }

            return Convert.ToBase64String(result);
        }

        private static string XORDecrypt(string data, string key)
        {
            if (string.IsNullOrEmpty(key))
                return data;

            var dataBytes = Convert.FromBase64String(data);
            var keyBytes = Encoding.UTF8.GetBytes(key);
            var result = new byte[dataBytes.Length];

            for (int i = 0; i < dataBytes.Length; i++)
            {
                result[i] = (byte)(dataBytes[i] ^ keyBytes[i % keyBytes.Length]);
            }

            return Encoding.UTF8.GetString(result);
        }

        private static string Base64Encode(string data)
        {
            var bytes = Encoding.UTF8.GetBytes(data);
            return Convert.ToBase64String(bytes);
        }

        private static string Base64Decode(string data)
        {
            var bytes = Convert.FromBase64String(data);
            return Encoding.UTF8.GetString(bytes);
        }

        private static string AESEncrypt(string data, string key)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("AES加密需要密钥");

            // 确保密钥长度为32字节
            var keyBytes = GetValidAESKey(key);
            var dataBytes = Encoding.UTF8.GetBytes(data);

            using (var aes = Aes.Create())
            {
                aes.Key = keyBytes;
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;

                // 生成随机IV
                aes.GenerateIV();

                using (var encryptor = aes.CreateEncryptor())
                {
                    var encrypted = encryptor.TransformFinalBlock(dataBytes, 0, dataBytes.Length);
                    
                    // 将IV和加密数据组合
                    var result = new byte[aes.IV.Length + encrypted.Length];
                    Array.Copy(aes.IV, 0, result, 0, aes.IV.Length);
                    Array.Copy(encrypted, 0, result, aes.IV.Length, encrypted.Length);

                    return Convert.ToBase64String(result);
                }
            }
        }

        private static string AESDecrypt(string data, string key)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("AES解密需要密钥");

            var keyBytes = GetValidAESKey(key);
            var dataBytes = Convert.FromBase64String(data);

            using (var aes = Aes.Create())
            {
                aes.Key = keyBytes;
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;

                // 提取IV
                var iv = new byte[16];
                Array.Copy(dataBytes, 0, iv, 0, 16);
                aes.IV = iv;

                // 提取加密数据
                var encrypted = new byte[dataBytes.Length - 16];
                Array.Copy(dataBytes, 16, encrypted, 0, encrypted.Length);

                using (var decryptor = aes.CreateDecryptor())
                {
                    var decrypted = decryptor.TransformFinalBlock(encrypted, 0, encrypted.Length);
                    return Encoding.UTF8.GetString(decrypted);
                }
            }
        }

        private static byte[] GetValidAESKey(string key)
        {
            var keyBytes = Encoding.UTF8.GetBytes(key);
            var validKey = new byte[32]; // AES-256需要32字节密钥

            if (keyBytes.Length >= 32)
            {
                Array.Copy(keyBytes, validKey, 32);
            }
            else
            {
                Array.Copy(keyBytes, validKey, keyBytes.Length);
                // 用重复的密钥填充剩余部分
                for (int i = keyBytes.Length; i < 32; i++)
                {
                    validKey[i] = keyBytes[i % keyBytes.Length];
                }
            }

            return validKey;
        }

        public static string EncryptJSON(string jsonData, EncryptionMethod method, string key = "")
        {
            if (string.IsNullOrEmpty(jsonData))
                return jsonData;

            try
            {
                // 先加密
                var encrypted = EncryptData(jsonData, method, key);
                
                // 如果不是Base64方法，需要再进行Base64编码以便传输
                if (method != EncryptionMethod.Base64)
                {
                    return Convert.ToBase64String(Encoding.UTF8.GetBytes(encrypted));
                }
                
                return encrypted;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] JSON加密失败: {ex.Message}");
                return jsonData;
            }
        }

        public static string DecryptJSON(string encodedData, EncryptionMethod method, string key = "")
        {
            if (string.IsNullOrEmpty(encodedData))
                return encodedData;

            try
            {
                string dataToDecrypt = encodedData;
                
                // 如果不是Base64方法，需要先进行Base64解码
                if (method != EncryptionMethod.Base64)
                {
                    var bytes = Convert.FromBase64String(encodedData);
                    dataToDecrypt = Encoding.UTF8.GetString(bytes);
                }
                
                // 然后解密
                return DecryptData(dataToDecrypt, method, key);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] JSON解密失败: {ex.Message}");
                return encodedData;
            }
        }

        public static EncryptionMethod ParseEncryptionMethod(string method)
        {
            return method?.ToLower() switch
            {
                "none" => EncryptionMethod.None,
                "xor" => EncryptionMethod.XOR,
                "base64" => EncryptionMethod.Base64,
                "aes" => EncryptionMethod.AES,
                _ => EncryptionMethod.None
            };
        }
    }
}
