@echo off
title GateSentinel 系统测试

echo ========================================
echo   GateSentinel 系统集成测试
echo ========================================
echo.

REM 设置测试变量
set SERVER_URL=http://localhost:8080
set TEST_USER=admin
set TEST_PASS=admin123

echo [INFO] 开始系统测试...
echo [INFO] 服务器地址: %SERVER_URL%
echo.

REM 测试1: 检查服务器是否运行
echo [TEST 1] 检查服务器状态...
curl -s -o nul -w "HTTP状态码: %%{http_code}" %SERVER_URL%/health
if %ERRORLEVEL% NEQ 0 (
    echo [FAIL] 服务器未运行或无法访问
    echo 请先启动服务器: start.bat
    pause
    exit /b 1
)
echo [PASS] 服务器正常运行
echo.

REM 测试2: 测试登录API
echo [TEST 2] 测试用户登录...
curl -s -X POST -H "Content-Type: application/json" ^
     -d "{\"username\":\"%TEST_USER%\",\"password\":\"%TEST_PASS%\"}" ^
     %SERVER_URL%/api/v1/login > temp_login.json

if %ERRORLEVEL% NEQ 0 (
    echo [FAIL] 登录请求失败
    goto cleanup
)

REM 检查登录响应
findstr "token" temp_login.json >nul
if %ERRORLEVEL% NEQ 0 (
    echo [FAIL] 登录失败，未获取到token
    type temp_login.json
    goto cleanup
)
echo [PASS] 用户登录成功
echo.

REM 测试3: 测试Beacon注册
echo [TEST 3] 测试Beacon注册...
curl -s -X POST -H "Content-Type: application/json" ^
     -d "{\"hostname\":\"test-host\",\"username\":\"test-user\",\"os_info\":\"Windows 10\",\"arch\":\"x64\",\"process_name\":\"test.exe\",\"process_id\":1234,\"external_ip\":\"\"}" ^
     %SERVER_URL%/api/v1/beacon/register > temp_beacon.json

if %ERRORLEVEL% NEQ 0 (
    echo [FAIL] Beacon注册请求失败
    goto cleanup
)

REM 检查注册响应
findstr "beacon_id" temp_beacon.json >nul
if %ERRORLEVEL% NEQ 0 (
    echo [FAIL] Beacon注册失败
    type temp_beacon.json
    goto cleanup
)
echo [PASS] Beacon注册成功
echo.

REM 测试4: 检查Web界面
echo [TEST 4] 检查Web界面...
curl -s -o nul -w "HTTP状态码: %%{http_code}" %SERVER_URL%/
if %ERRORLEVEL% NEQ 0 (
    echo [FAIL] Web界面无法访问
    goto cleanup
)
echo [PASS] Web界面可正常访问
echo.

REM 测试5: 检查静态资源
echo [TEST 5] 检查静态资源...
curl -s -o nul -w "HTTP状态码: %%{http_code}" %SERVER_URL%/static/css/style.css
if %ERRORLEVEL% NEQ 0 (
    echo [FAIL] 静态资源无法访问
    goto cleanup
)
echo [PASS] 静态资源可正常访问
echo.

echo ========================================
echo   所有测试通过！
echo ========================================
echo.
echo 系统已准备就绪，可以开始使用：
echo.
echo 1. Web界面: %SERVER_URL%
echo 2. 默认账户: admin / admin123
echo 3. 操作员账户: operator / operator123
echo.
echo 接下来可以：
echo - 编译并运行Beacon客户端
echo - 通过Web界面管理Beacon
echo - 执行远程命令
echo.

:cleanup
if exist temp_login.json del temp_login.json
if exist temp_beacon.json del temp_beacon.json

echo 按任意键退出...
pause >nul
