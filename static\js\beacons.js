// Beacon管理页面JavaScript

let allBeacons = [];
let currentBeaconId = null;
let autoRefreshInterval = null;
let isAutoRefreshEnabled = false;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async () => {
    await loadBeacons();
    await loadStats();
});

// 加载Beacon列表
async function loadBeacons() {
    try {
        const response = await apiGet('/beacons');
        allBeacons = response.beacons || [];
        displayBeacons(allBeacons);
    } catch (error) {
        console.error('加载Beacon列表失败:', error);
        showError('加载Beacon列表失败: ' + error.message);
        document.getElementById('beaconsTable').innerHTML = `
            <tr>
                <td colspan="11" style="text-align: center; padding: 2rem; color: #dc3545;">
                    加载失败: ${error.message}
                </td>
            </tr>
        `;
    }
}

// 加载统计信息
async function loadStats() {
    try {
        const stats = await apiGet('/beacons/stats');
        document.getElementById('totalBeacons').textContent = stats.total_beacons || 0;
        document.getElementById('onlineBeacons').textContent = stats.online_beacons || 0;
        document.getElementById('offlineBeacons').textContent = stats.offline_beacons || 0;
    } catch (error) {
        console.error('加载统计信息失败:', error);
    }
}

// 显示Beacon列表
function displayBeacons(beacons) {
    const tbody = document.getElementById('beaconsTable');
    
    if (beacons.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="11" style="text-align: center; padding: 2rem; color: #666;">
                    暂无Beacon数据
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = beacons.map(beacon => `
        <tr>
            <td>
                <span class="tooltip">
                    ${beacon.beacon_id.substring(0, 8)}...
                    <span class="tooltiptext">${beacon.beacon_id}</span>
                </span>
            </td>
            <td>${beacon.hostname || '-'}</td>
            <td>${beacon.username || '-'}</td>
            <td>${beacon.os_info || '-'}</td>
            <td>${beacon.arch || '-'}</td>
            <td>${beacon.process_name || '-'} (${beacon.process_id || '-'})</td>
            <td>${beacon.ip_address || '-'}</td>
            <td>${formatDateTime(beacon.first_seen)}</td>
            <td>${formatRelativeTime(beacon.last_seen)}</td>
            <td>
                <span class="status ${beacon.is_online ? 'online' : 'offline'}">
                    ${beacon.is_online ? '在线' : '离线'}
                </span>
            </td>
            <td>
                <button class="btn btn-primary btn-sm" onclick="viewBeacon('${beacon.beacon_id}')">
                    查看
                </button>
                <button class="btn btn-secondary btn-sm" onclick="configBeacon('${beacon.beacon_id}')">
                    配置
                </button>
                <button class="btn btn-success btn-sm" onclick="sendCommand('${beacon.beacon_id}')">
                    命令
                </button>
                <button class="btn btn-danger btn-sm" onclick="deleteBeacon('${beacon.beacon_id}')">
                    删除
                </button>
            </td>
        </tr>
    `).join('');
}

// 过滤Beacon
function filterBeacons() {
    const onlineOnly = document.getElementById('onlineOnlyFilter').checked;
    
    if (onlineOnly) {
        const onlineBeacons = allBeacons.filter(beacon => beacon.is_online);
        displayBeacons(onlineBeacons);
    } else {
        displayBeacons(allBeacons);
    }
}

// 刷新Beacon列表
async function refreshBeacons() {
    await loadBeacons();
    await loadStats();
    showSuccess('Beacon列表已刷新');
}

// 切换自动刷新
function toggleAutoRefresh() {
    if (isAutoRefreshEnabled) {
        clearInterval(autoRefreshInterval);
        isAutoRefreshEnabled = false;
        document.getElementById('autoRefreshText').textContent = '开启自动刷新';
        showInfo('自动刷新已关闭');
    } else {
        autoRefreshInterval = setInterval(async () => {
            await loadBeacons();
            await loadStats();
        }, 30000); // 30秒刷新一次
        isAutoRefreshEnabled = true;
        document.getElementById('autoRefreshText').textContent = '关闭自动刷新';
        showInfo('自动刷新已开启（30秒间隔）');
    }
}

// 查看Beacon详情
async function viewBeacon(beaconId) {
    try {
        const beacon = await apiGet(`/beacons/${beaconId}`);
        currentBeaconId = beaconId;
        
        const details = `
            <div class="beacon-details">
                <div class="detail-row">
                    <strong>Beacon ID:</strong> ${beacon.beacon.beacon_id}
                </div>
                <div class="detail-row">
                    <strong>主机名:</strong> ${beacon.beacon.hostname || '-'}
                </div>
                <div class="detail-row">
                    <strong>用户:</strong> ${beacon.beacon.username || '-'}
                </div>
                <div class="detail-row">
                    <strong>操作系统:</strong> ${beacon.beacon.os_info || '-'}
                </div>
                <div class="detail-row">
                    <strong>架构:</strong> ${beacon.beacon.arch || '-'}
                </div>
                <div class="detail-row">
                    <strong>进程:</strong> ${beacon.beacon.process_name || '-'} (PID: ${beacon.beacon.process_id || '-'})
                </div>
                <div class="detail-row">
                    <strong>IP地址:</strong> ${beacon.beacon.ip_address || '-'}
                </div>
                <div class="detail-row">
                    <strong>外部IP:</strong> ${beacon.beacon.external_ip || '-'}
                </div>
                <div class="detail-row">
                    <strong>心跳间隔:</strong> ${beacon.beacon.sleep_time || '-'} 秒
                </div>
                <div class="detail-row">
                    <strong>抖动:</strong> ${beacon.beacon.jitter || '-'}%
                </div>
                <div class="detail-row">
                    <strong>状态:</strong> 
                    <span class="status ${beacon.beacon.is_online ? 'online' : 'offline'}">
                        ${beacon.beacon.is_online ? '在线' : '离线'}
                    </span>
                </div>
                <div class="detail-row">
                    <strong>首次上线:</strong> ${formatDateTime(beacon.beacon.first_seen)}
                </div>
                <div class="detail-row">
                    <strong>最后上线:</strong> ${formatDateTime(beacon.beacon.last_seen)}
                </div>
            </div>
        `;
        
        document.getElementById('beaconDetails').innerHTML = details;
        document.getElementById('beaconModal').style.display = 'block';
    } catch (error) {
        showError('获取Beacon详情失败: ' + error.message);
    }
}

// 配置Beacon
async function configBeacon(beaconId) {
    try {
        const beacon = await apiGet(`/beacons/${beaconId}`);
        currentBeaconId = beaconId;
        
        document.getElementById('sleepTime').value = beacon.beacon.sleep_time || 60;
        document.getElementById('jitter').value = beacon.beacon.jitter || 10;
        document.getElementById('configModal').style.display = 'block';
    } catch (error) {
        showError('获取Beacon配置失败: ' + error.message);
    }
}

// 保存Beacon配置
async function saveBeaconConfig() {
    if (!currentBeaconId) return;
    
    const sleepTime = parseInt(document.getElementById('sleepTime').value);
    const jitter = parseInt(document.getElementById('jitter').value);
    
    if (sleepTime < 1 || sleepTime > 3600) {
        showError('心跳间隔必须在1-3600秒之间');
        return;
    }
    
    if (jitter < 0 || jitter > 100) {
        showError('抖动必须在0-100%之间');
        return;
    }
    
    try {
        await apiPut(`/beacons/${currentBeaconId}/config`, {
            sleep_time: sleepTime,
            jitter: jitter
        });
        
        showSuccess('Beacon配置已更新');
        closeConfigModal();
        await refreshBeacons();
    } catch (error) {
        showError('更新Beacon配置失败: ' + error.message);
    }
}

// 发送命令
function sendCommand(beaconId) {
    window.location.href = `/commands?beacon=${beaconId}`;
}

// 删除Beacon
async function deleteBeacon(beaconId) {
    if (!confirm('确定要删除这个Beacon吗？此操作不可恢复。')) {
        return;
    }
    
    try {
        await apiDelete(`/beacons/${beaconId}`);
        showSuccess('Beacon已删除');
        await refreshBeacons();
    } catch (error) {
        showError('删除Beacon失败: ' + error.message);
    }
}

// 关闭模态框
function closeBeaconModal() {
    document.getElementById('beaconModal').style.display = 'none';
    currentBeaconId = null;
}

function closeConfigModal() {
    document.getElementById('configModal').style.display = 'none';
    currentBeaconId = null;
}

function sendCommandToBeacon() {
    if (currentBeaconId) {
        window.location.href = `/commands?beacon=${currentBeaconId}`;
    }
}

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
});
