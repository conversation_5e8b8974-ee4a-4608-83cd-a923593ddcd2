@echo off
echo ========================================
echo   GateSentinel 编译测试
echo ========================================
echo.

cd backend

echo [INFO] 检查Go模块...
go mod tidy

echo.
echo [INFO] 编译测试...
go build -o gatesentinel.exe

if %ERRORLEVEL% EQU 0 (
    echo [SUCCESS] 编译成功！
    echo.
    echo [INFO] 启动服务器进行测试...
    echo [INFO] 按 Ctrl+C 停止服务器
    echo.
    .\gatesentinel.exe
) else (
    echo [ERROR] 编译失败！
    pause
    exit /b 1
)

pause
