package main

import (
	"log"
	"os"
	"path/filepath"
	"time"

	"gatesentinel/config"
	"gatesentinel/database"
	"gatesentinel/handlers"
	"gatesentinel/middleware"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	configPath := "config.yaml"
	if len(os.Args) > 1 {
		configPath = os.Args[1]
	}

	// 如果配置文件不存在，尝试上级目录
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		configPath = "../config.yaml"
	}

	err := config.LoadConfig(configPath)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化数据库
	err = database.InitDatabase(config.GetDatabasePath())
	if err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}
	defer database.CloseDatabase()

	// 执行数据库初始化脚本
	schemaPath := filepath.Join("..", "db", "init.sql")
	if _, err := os.Stat(schemaPath); err == nil {
		err = database.ExecuteSchema(schemaPath)
		if err != nil {
			log.Printf("执行数据库脚本失败: %v", err)
		}
	}

	// 设置Gin模式
	if !config.IsDebugMode() {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建Gin引擎
	r := gin.Default()

	// 添加CORS中间件
	r.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 静态文件服务
	r.Static("/static", config.AppConfig.Web.StaticPath)
	r.LoadHTMLGlob(config.AppConfig.Web.TemplatePath + "/*")

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"time":   time.Now().Unix(),
		})
	})

	// 公开API路由（无需认证）
	public := r.Group("/api/v1")
	{
		// 认证相关
		public.POST("/login", handlers.Login)
		public.POST("/validate-token", handlers.ValidateToken)

		// Beacon通信接口（无需Web认证）
		public.POST("/beacon/register", handlers.RegisterBeacon)
		public.POST("/beacon/:id/heartbeat", handlers.BeaconHeartbeat)
		// public.GET("/beacon/:id/tasks", handlers.GetBeaconTasks)
		// public.POST("/beacon/:id/result", handlers.SubmitTaskResult)
	}

	// 需要认证的API路由
	protected := r.Group("/api/v1")
	protected.Use(middleware.AuthMiddleware())
	{
		// 用户相关
		protected.GET("/profile", handlers.GetProfile)
		protected.POST("/change-password", handlers.ChangePassword)
		protected.POST("/logout", handlers.Logout)

		// Beacon管理（需要操作员权限）
		beacons := protected.Group("/beacons")
		beacons.Use(middleware.OperatorMiddleware())
		{
			beacons.GET("", handlers.GetBeacons)
			beacons.GET("/stats", handlers.GetBeaconStats)
			beacons.GET("/:id", handlers.GetBeacon)
			beacons.PUT("/:id/config", handlers.UpdateBeaconConfig)
			beacons.DELETE("/:id", handlers.DeleteBeacon)
		}

		// 命令管理（需要操作员权限）
		// commands := protected.Group("/commands")
		// commands.Use(middleware.OperatorMiddleware())
		// {
		//     commands.POST("", handlers.CreateCommand)
		//     commands.GET("", handlers.GetCommands)
		//     commands.GET("/:id", handlers.GetCommand)
		//     commands.GET("/:id/result", handlers.GetCommandResult)
		// }

		// 管理员功能
		// admin := protected.Group("/admin")
		// admin.Use(middleware.AdminMiddleware())
		// {
		//     admin.GET("/users", handlers.GetUsers)
		//     admin.POST("/users", handlers.CreateUser)
		//     admin.PUT("/users/:id", handlers.UpdateUser)
		//     admin.DELETE("/users/:id", handlers.DeleteUser)
		//     admin.GET("/logs", handlers.GetSystemLogs)
		//     admin.GET("/stats", handlers.GetSystemStats)
		// }
	}

	// Web界面路由
	r.GET("/", func(c *gin.Context) {
		c.HTML(200, "index.html", gin.H{
			"title": "GateSentinel",
		})
	})

	r.GET("/login", func(c *gin.Context) {
		c.HTML(200, "login.html", gin.H{
			"title": "登录 - GateSentinel",
		})
	})

	// 启动定时任务检查Beacon超时
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for range ticker.C {
			err := database.CheckBeaconTimeout(config.GetBeaconTimeout())
			if err != nil {
				log.Printf("检查Beacon超时失败: %v", err)
			}
		}
	}()

	// 启动服务器
	log.Printf("GateSentinel服务器启动在 %s", config.GetServerAddress())
	log.Printf("Web界面: http://%s", config.GetServerAddress())
	
	if err := r.Run(config.GetServerAddress()); err != nil {
		log.Fatalf("启动服务器失败: %v", err)
	}
}
