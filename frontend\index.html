<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GateSentinel - 准入控制与Beacon管理系统</title>
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <div class="logo">GateSentinel</div>
            <ul class="nav-links">
                <li><a href="/" class="active">仪表板</a></li>
                <li><a href="/beacons">Beacon管理</a></li>
                <li><a href="/commands">命令执行</a></li>
                <li><a href="/shellcodes">Shellcode管理</a></li>
            </ul>
            <div class="user-info">
                <span>欢迎，<span class="user-name">用户</span> (<span class="user-role">角色</span>)</span>
                <button class="btn btn-secondary btn-sm logout-btn">登出</button>
            </div>
        </div>
    </nav>

    <!-- 主内容 -->
    <div class="container">
        <!-- 统计卡片 -->
        <div class="dashboard-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalBeacons">-</div>
                <div class="stat-label">总Beacon数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="onlineBeacons">-</div>
                <div class="stat-label">在线Beacon</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="offlineBeacons">-</div>
                <div class="stat-label">离线Beacon</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalCommands">-</div>
                <div class="stat-label">执行命令数</div>
            </div>
        </div>

        <!-- 在线Beacon列表 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">在线Beacon</h2>
                <button class="btn btn-primary btn-sm" onclick="refreshBeacons()">刷新</button>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Beacon ID</th>
                            <th>主机名</th>
                            <th>用户</th>
                            <th>操作系统</th>
                            <th>架构</th>
                            <th>IP地址</th>
                            <th>最后上线</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="beaconsTable">
                        <tr>
                            <td colspan="9" style="text-align: center; padding: 2rem;">
                                <div class="loading"></div>
                                <span style="margin-left: 1rem;">加载中...</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 系统状态 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">系统状态</h2>
            </div>
            <div class="dashboard-grid">
                <div>
                    <h4>服务器状态</h4>
                    <p id="serverStatus" class="status online">运行中</p>
                </div>
                <div>
                    <h4>数据库状态</h4>
                    <p id="dbStatus" class="status online">正常</p>
                </div>
                <div>
                    <h4>系统时间</h4>
                    <p id="systemTime">-</p>
                </div>
                <div>
                    <h4>运行时长</h4>
                    <p id="uptime">-</p>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/utils.js"></script>
    <script>
        let refreshInterval;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', async () => {
            await loadDashboardData();
            startAutoRefresh();
            updateSystemTime();
        });

        // 加载仪表板数据
        async function loadDashboardData() {
            try {
                // 加载Beacon统计
                await loadBeaconStats();
                
                // 加载在线Beacon列表
                await loadOnlineBeacons();
                
            } catch (error) {
                console.error('加载仪表板数据失败:', error);
                showError('加载数据失败: ' + error.message);
            }
        }

        // 加载Beacon统计
        async function loadBeaconStats() {
            try {
                const stats = await apiGet('/beacons/stats');
                
                document.getElementById('totalBeacons').textContent = stats.total_beacons || 0;
                document.getElementById('onlineBeacons').textContent = stats.online_beacons || 0;
                document.getElementById('offlineBeacons').textContent = stats.offline_beacons || 0;
                // document.getElementById('totalCommands').textContent = stats.total_commands || 0;
                
            } catch (error) {
                console.error('加载Beacon统计失败:', error);
            }
        }

        // 加载在线Beacon列表
        async function loadOnlineBeacons() {
            try {
                const response = await apiGet('/beacons?online=true');
                const beacons = response.beacons || [];
                
                const tbody = document.getElementById('beaconsTable');
                
                if (beacons.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="9" style="text-align: center; padding: 2rem; color: #666;">
                                暂无在线Beacon
                            </td>
                        </tr>
                    `;
                    return;
                }

                tbody.innerHTML = beacons.map(beacon => `
                    <tr>
                        <td>
                            <span class="tooltip">
                                ${beacon.beacon_id.substring(0, 8)}...
                                <span class="tooltiptext">${beacon.beacon_id}</span>
                            </span>
                        </td>
                        <td>${beacon.hostname || '-'}</td>
                        <td>${beacon.username || '-'}</td>
                        <td>${beacon.os_info || '-'}</td>
                        <td>${beacon.arch || '-'}</td>
                        <td>${beacon.ip_address || '-'}</td>
                        <td>${formatRelativeTime(beacon.last_seen)}</td>
                        <td>
                            <span class="status ${beacon.is_online ? 'online' : 'offline'}">
                                ${beacon.is_online ? '在线' : '离线'}
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-primary btn-sm" onclick="viewBeacon('${beacon.beacon_id}')">
                                查看
                            </button>
                            <button class="btn btn-secondary btn-sm" onclick="sendCommand('${beacon.beacon_id}')">
                                命令
                            </button>
                        </td>
                    </tr>
                `).join('');
                
            } catch (error) {
                console.error('加载在线Beacon失败:', error);
                document.getElementById('beaconsTable').innerHTML = `
                    <tr>
                        <td colspan="9" style="text-align: center; padding: 2rem; color: #dc3545;">
                            加载失败: ${error.message}
                        </td>
                    </tr>
                `;
            }
        }

        // 刷新Beacon数据
        async function refreshBeacons() {
            await loadDashboardData();
            showSuccess('数据已刷新');
        }

        // 查看Beacon详情
        function viewBeacon(beaconId) {
            window.location.href = `/beacons#${beaconId}`;
        }

        // 发送命令
        function sendCommand(beaconId) {
            window.location.href = `/commands?beacon=${beaconId}`;
        }

        // 开始自动刷新
        function startAutoRefresh() {
            refreshInterval = setInterval(async () => {
                await loadDashboardData();
            }, 30000); // 30秒刷新一次
        }

        // 停止自动刷新
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        }

        // 更新系统时间
        function updateSystemTime() {
            function update() {
                const now = new Date();
                document.getElementById('systemTime').textContent = now.toLocaleString('zh-CN');
            }
            
            update();
            setInterval(update, 1000);
        }

        // 页面卸载时停止自动刷新
        window.addEventListener('beforeunload', stopAutoRefresh);
    </script>
</body>
</html>
