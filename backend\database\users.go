package database

import (
	"database/sql"
	"fmt"
	"log"
	"time"

	"golang.org/x/crypto/bcrypt"
)

// User 用户结构体
type User struct {
	ID           int       `json:"id"`
	Username     string    `json:"username"`
	PasswordHash string    `json:"-"` // 不在JSON中显示密码哈希
	Email        string    `json:"email"`
	Role         string    `json:"role"`
	IsActive     bool      `json:"is_active"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	LastLogin    *time.Time `json:"last_login"`
}

// CreateUser 创建新用户
func CreateUser(user *User) error {
	query := `
		INSERT INTO users (username, password_hash, email, role, is_active)
		VALUES (?, ?, ?, ?, ?)
	`
	result, err := DB.Exec(query, user.Username, user.PasswordHash, user.Email, user.Role, user.IsActive)
	if err != nil {
		return fmt.Errorf("创建用户失败: %v", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取用户ID失败: %v", err)
	}

	user.ID = int(id)
	return nil
}

// GetUserByUsername 根据用户名获取用户
func GetUserByUsername(username string) (*User, error) {
	user := &User{}
	query := `
		SELECT id, username, password_hash, email, role, is_active, 
		       created_at, updated_at, last_login
		FROM users 
		WHERE username = ? AND is_active = 1
	`
	
	var lastLogin sql.NullTime
	err := DB.QueryRow(query, username).Scan(
		&user.ID, &user.Username, &user.PasswordHash, &user.Email,
		&user.Role, &user.IsActive, &user.CreatedAt, &user.UpdatedAt,
		&lastLogin,
	)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("用户不存在")
		}
		return nil, fmt.Errorf("查询用户失败: %v", err)
	}

	if lastLogin.Valid {
		user.LastLogin = &lastLogin.Time
	}

	return user, nil
}

// GetUserByID 根据ID获取用户
func GetUserByID(id int) (*User, error) {
	user := &User{}
	query := `
		SELECT id, username, password_hash, email, role, is_active, 
		       created_at, updated_at, last_login
		FROM users 
		WHERE id = ? AND is_active = 1
	`
	
	var lastLogin sql.NullTime
	err := DB.QueryRow(query, id).Scan(
		&user.ID, &user.Username, &user.PasswordHash, &user.Email,
		&user.Role, &user.IsActive, &user.CreatedAt, &user.UpdatedAt,
		&lastLogin,
	)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("用户不存在")
		}
		return nil, fmt.Errorf("查询用户失败: %v", err)
	}

	if lastLogin.Valid {
		user.LastLogin = &lastLogin.Time
	}

	return user, nil
}

// UpdateUserLastLogin 更新用户最后登录时间
func UpdateUserLastLogin(userID int) error {
	query := `UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?`
	_, err := DB.Exec(query, userID)
	if err != nil {
		return fmt.Errorf("更新用户登录时间失败: %v", err)
	}
	return nil
}

// GetAllUsers 获取所有用户列表
func GetAllUsers() ([]*User, error) {
	query := `
		SELECT id, username, email, role, is_active, 
		       created_at, updated_at, last_login
		FROM users 
		ORDER BY created_at DESC
	`
	
	rows, err := DB.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询用户列表失败: %v", err)
	}
	defer rows.Close()

	var users []*User
	for rows.Next() {
		user := &User{}
		var lastLogin sql.NullTime
		
		err := rows.Scan(
			&user.ID, &user.Username, &user.Email, &user.Role,
			&user.IsActive, &user.CreatedAt, &user.UpdatedAt,
			&lastLogin,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描用户数据失败: %v", err)
		}

		if lastLogin.Valid {
			user.LastLogin = &lastLogin.Time
		}

		users = append(users, user)
	}

	return users, nil
}

// UpdateUser 更新用户信息
func UpdateUser(user *User) error {
	query := `
		UPDATE users 
		SET email = ?, role = ?, is_active = ?
		WHERE id = ?
	`
	_, err := DB.Exec(query, user.Email, user.Role, user.IsActive, user.ID)
	if err != nil {
		return fmt.Errorf("更新用户失败: %v", err)
	}
	return nil
}

// UpdateUserPassword 更新用户密码
func UpdateUserPassword(userID int, passwordHash string) error {
	query := `UPDATE users SET password_hash = ? WHERE id = ?`
	_, err := DB.Exec(query, passwordHash, userID)
	if err != nil {
		return fmt.Errorf("更新用户密码失败: %v", err)
	}
	return nil
}

// DeleteUser 删除用户（软删除）
func DeleteUser(userID int) error {
	query := `UPDATE users SET is_active = 0 WHERE id = ?`
	_, err := DB.Exec(query, userID)
	if err != nil {
		return fmt.Errorf("删除用户失败: %v", err)
	}
	return nil
}

// CreateDefaultUsers 创建默认用户
func CreateDefaultUsers() error {
	// 检查是否已经有用户
	var count int
	err := DB.QueryRow("SELECT COUNT(*) FROM users").Scan(&count)
	if err != nil {
		return fmt.Errorf("检查用户数量失败: %v", err)
	}

	if count > 0 {
		log.Printf("数据库中已有 %d 个用户，跳过默认用户创建", count)
		return nil
	}

	// 生成admin密码哈希
	adminHash, err := bcrypt.GenerateFromPassword([]byte("admin123"), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("生成admin密码哈希失败: %v", err)
	}

	// 生成operator密码哈希
	operatorHash, err := bcrypt.GenerateFromPassword([]byte("operator123"), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("生成operator密码哈希失败: %v", err)
	}

	// 创建admin用户
	_, err = DB.Exec(`
		INSERT INTO users (username, password_hash, email, role, is_active)
		VALUES (?, ?, ?, ?, ?)
	`, "admin", string(adminHash), "<EMAIL>", "admin", true)
	if err != nil {
		return fmt.Errorf("创建admin用户失败: %v", err)
	}

	// 创建operator用户
	_, err = DB.Exec(`
		INSERT INTO users (username, password_hash, email, role, is_active)
		VALUES (?, ?, ?, ?, ?)
	`, "operator", string(operatorHash), "<EMAIL>", "operator", true)
	if err != nil {
		return fmt.Errorf("创建operator用户失败: %v", err)
	}

	log.Println("默认用户创建完成:")
	log.Println("  管理员: admin / admin123")
	log.Println("  操作员: operator / operator123")

	return nil
}
