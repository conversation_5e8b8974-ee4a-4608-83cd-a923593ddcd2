@echo off
echo Quick Build Test
echo ================

REM 清理之前的文件
if exist "bin\beacon_simple.exe" del "bin\beacon_simple.exe"
if exist "*.obj" del "*.obj"

REM 创建输出目录
if not exist "bin" mkdir bin

echo [INFO] Compiling beacon_simple.c...

REM 编译命令
cl /nologo /O2 /MT /DWIN32 /D_WIN32 /D_CRT_SECURE_NO_WARNINGS beacon_simple.c /Fe:bin\beacon_simple.exe /link wininet.lib kernel32.lib user32.lib advapi32.lib

echo.
echo [INFO] Checking compilation result...

if exist "bin\beacon_simple.exe" (
    echo [SUCCESS] Compilation successful!
    echo.
    echo File info:
    dir bin\beacon_simple.exe
    echo.
    echo [INFO] Testing beacon...
    echo Press Ctrl+C to stop the test
    echo.
    bin\beacon_simple.exe
) else (
    echo [ERROR] Compilation failed!
    echo.
    echo The executable was not created.
    echo Check the error messages above.
)

REM 清理临时文件
del *.obj >nul 2>nul

pause
