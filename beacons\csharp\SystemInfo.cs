using System;
using System.Diagnostics;
using System.Management;
using System.Runtime.InteropServices;
using System.Security.Principal;
using System.Threading.Tasks;

namespace GateSentinel.Beacon
{
    public class SystemInfo
    {
        public string Hostname { get; private set; } = string.Empty;
        public string Username { get; private set; } = string.Empty;
        public string OSInfo { get; private set; } = string.Empty;
        public string Architecture { get; private set; } = string.Empty;
        public string ProcessName { get; private set; } = string.Empty;
        public int ProcessId { get; private set; }
        public string Domain { get; private set; } = string.Empty;
        public bool IsElevated { get; private set; }

        public async Task CollectAsync()
        {
            await Task.Run(() =>
            {
                CollectBasicInfo();
                CollectOSInfo();
                CollectProcessInfo();
                CollectUserInfo();
            });
        }

        private void CollectBasicInfo()
        {
            try
            {
                // 主机名
                Hostname = Environment.MachineName;

                // 用户名
                Username = Environment.UserName;

                // 域名
                Domain = Environment.UserDomainName;

                // 架构
                Architecture = RuntimeInformation.ProcessArchitecture.ToString().ToLower();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[WARNING] 收集基本信息失败: {ex.Message}");
            }
        }

        private void CollectOSInfo()
        {
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    CollectWindowsOSInfo();
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    OSInfo = "Linux " + RuntimeInformation.OSDescription;
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                {
                    OSInfo = "macOS " + RuntimeInformation.OSDescription;
                }
                else
                {
                    OSInfo = RuntimeInformation.OSDescription;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[WARNING] 收集操作系统信息失败: {ex.Message}");
                OSInfo = "Unknown OS";
            }
        }

        private void CollectWindowsOSInfo()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_OperatingSystem"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        var caption = obj["Caption"]?.ToString() ?? "Windows";
                        var version = obj["Version"]?.ToString() ?? "";
                        var architecture = obj["OSArchitecture"]?.ToString() ?? "";
                        
                        OSInfo = $"{caption} {version}";
                        if (!string.IsNullOrEmpty(architecture))
                        {
                            OSInfo += $" ({architecture})";
                        }
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[WARNING] 使用WMI收集Windows信息失败: {ex.Message}");
                OSInfo = "Windows " + Environment.OSVersion.VersionString;
            }
        }

        private void CollectProcessInfo()
        {
            try
            {
                var currentProcess = Process.GetCurrentProcess();
                ProcessId = currentProcess.Id;
                ProcessName = currentProcess.ProcessName + ".exe";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[WARNING] 收集进程信息失败: {ex.Message}");
                ProcessName = "beacon.exe";
                ProcessId = 0;
            }
        }

        private void CollectUserInfo()
        {
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    // 检查是否以管理员权限运行
                    using (var identity = WindowsIdentity.GetCurrent())
                    {
                        var principal = new WindowsPrincipal(identity);
                        IsElevated = principal.IsInRole(WindowsBuiltInRole.Administrator);
                    }
                }
                else
                {
                    // Linux/Unix系统检查是否为root用户
                    IsElevated = Environment.UserName == "root";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[WARNING] 检查用户权限失败: {ex.Message}");
                IsElevated = false;
            }
        }

        public string GetFullUsername()
        {
            if (!string.IsNullOrEmpty(Domain) && Domain != Hostname)
            {
                return $"{Domain}\\{Username}";
            }
            return Username;
        }

        public override string ToString()
        {
            return $"SystemInfo {{ " +
                   $"Hostname: {Hostname}, " +
                   $"Username: {GetFullUsername()}, " +
                   $"OS: {OSInfo}, " +
                   $"Arch: {Architecture}, " +
                   $"Process: {ProcessName} (PID: {ProcessId}), " +
                   $"Elevated: {IsElevated} }}";
        }
    }
}
