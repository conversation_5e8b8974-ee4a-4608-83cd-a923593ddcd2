#!/bin/bash

echo "========================================"
echo "  GateSentinel 快速启动脚本"
echo "========================================"
echo ""

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "[ERROR] Go未安装或未添加到PATH环境变量"
    echo "请访问 https://golang.org/dl/ 下载安装Go"
    exit 1
fi

echo "[INFO] 检测到Go版本:"
go version

# 检查SQLite
if ! command -v sqlite3 &> /dev/null; then
    echo "[WARNING] SQLite3未找到，将使用Go内置的SQLite驱动"
fi

echo ""
echo "[INFO] 准备启动GateSentinel服务器..."
echo ""

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_DIR="$SCRIPT_DIR/backend"

# 切换到后端目录
cd "$BACKEND_DIR"

# 检查go.mod文件
if [ ! -f "go.mod" ]; then
    echo "[ERROR] go.mod文件不存在，请确保在正确的目录中"
    exit 1
fi

# 下载依赖
echo "[INFO] 下载Go模块依赖..."
go mod tidy

if [ $? -ne 0 ]; then
    echo "[ERROR] 下载依赖失败"
    exit 1
fi

# 初始化数据库目录
echo "[INFO] 初始化数据库..."
mkdir -p "../db"

# 启动服务器
echo "[INFO] 启动GateSentinel服务器..."
echo "[INFO] Web界面将在 http://localhost:8080 启动"
echo "[INFO] 按 Ctrl+C 停止服务器"
echo ""
echo "默认登录账户："
echo "  管理员: admin / admin123"
echo "  操作员: operator / operator123"
echo ""
echo "========================================"

go run main.go

if [ $? -ne 0 ]; then
    echo ""
    echo "[ERROR] 服务器启动失败"
    exit 1
fi
