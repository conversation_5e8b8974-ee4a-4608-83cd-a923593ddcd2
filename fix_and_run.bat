@echo off
echo ========================================
echo   GateSentinel 修复和启动
echo ========================================
echo.

echo [INFO] 切换到backend目录...
cd backend

echo [INFO] 清理可能的缓存文件...
if exist "*.exe" del "*.exe"
if exist "go.sum" del "go.sum"

echo [INFO] 重新初始化Go模块...
go mod tidy

echo [INFO] 尝试编译...
go build -v

if %ERRORLEVEL% EQU 0 (
    echo [SUCCESS] 编译成功！
    echo.
    echo [INFO] 启动服务器...
    echo [INFO] Web界面: http://localhost:8080
    echo [INFO] 默认账户: admin/admin123, operator/operator123
    echo.
    go run main.go
) else (
    echo [ERROR] 编译失败！
    echo.
    echo [INFO] 尝试直接运行...
    go run main.go
)

pause
