-- GateSentinel 数据库初始化脚本
-- SQLite数据库表结构定义

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    role VARCHAR(20) DEFAULT 'operator', -- admin, operator, viewer
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME
);

-- Beacon表
CREATE TABLE IF NOT EXISTS beacons (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    beacon_id VARCHAR(64) UNIQUE NOT NULL, -- 唯一标识符
    hostname VARCHAR(100),
    username VARCHAR(50),
    os_info VARCHAR(200),
    arch VARCHAR(20),
    process_name <PERSON><PERSON><PERSON><PERSON>(100),
    process_id INTEGER,
    ip_address VARCHAR(45),
    external_ip VARCHAR(45),
    sleep_time INTEGER DEFAULT 60,
    jitter INTEGER DEFAULT 0,
    is_online BOOLEAN DEFAULT 1,
    first_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 命令表
CREATE TABLE IF NOT EXISTS commands (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    beacon_id VARCHAR(64) NOT NULL,
    command_type VARCHAR(20) NOT NULL, -- cmd, shell, powershell, upload, download
    command_text TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'pending', -- pending, sent, completed, failed
    created_by INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    sent_at DATETIME,
    completed_at DATETIME,
    FOREIGN KEY (beacon_id) REFERENCES beacons(beacon_id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 命令结果表
CREATE TABLE IF NOT EXISTS command_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    command_id INTEGER NOT NULL,
    result_text TEXT,
    error_text TEXT,
    exit_code INTEGER,
    execution_time INTEGER, -- 执行时间（毫秒）
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (command_id) REFERENCES commands(id)
);

-- Shellcode表
CREATE TABLE IF NOT EXISTS shellcodes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    shellcode_data BLOB NOT NULL,
    file_size INTEGER,
    file_hash VARCHAR(64),
    arch VARCHAR(20), -- x86, x64
    format VARCHAR(20), -- raw, exe, dll
    created_by INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Shellcode执行记录表
CREATE TABLE IF NOT EXISTS shellcode_executions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    shellcode_id INTEGER NOT NULL,
    beacon_id VARCHAR(64) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending', -- pending, sent, executed, failed
    created_by INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    executed_at DATETIME,
    FOREIGN KEY (shellcode_id) REFERENCES shellcodes(id),
    FOREIGN KEY (beacon_id) REFERENCES beacons(beacon_id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 系统日志表
CREATE TABLE IF NOT EXISTS system_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    level VARCHAR(10) NOT NULL, -- DEBUG, INFO, WARN, ERROR
    category VARCHAR(50), -- AUTH, BEACON, COMMAND, SYSTEM
    message TEXT NOT NULL,
    user_id INTEGER,
    beacon_id VARCHAR(64),
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (beacon_id) REFERENCES beacons(beacon_id)
);

-- 会话表
CREATE TABLE IF NOT EXISTS sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    user_id INTEGER NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    expires_at DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_beacons_beacon_id ON beacons(beacon_id);
CREATE INDEX IF NOT EXISTS idx_beacons_last_seen ON beacons(last_seen);
CREATE INDEX IF NOT EXISTS idx_beacons_is_online ON beacons(is_online);
CREATE INDEX IF NOT EXISTS idx_commands_beacon_id ON commands(beacon_id);
CREATE INDEX IF NOT EXISTS idx_commands_status ON commands(status);
CREATE INDEX IF NOT EXISTS idx_commands_created_at ON commands(created_at);
CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(level);
CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON sessions(expires_at);

-- 插入默认管理员用户 (密码: admin123)
-- 注意：在生产环境中应该修改默认密码
INSERT OR REPLACE INTO users (username, password_hash, email, role, is_active)
VALUES ('admin', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukx2.LvO2', '<EMAIL>', 'admin', 1);

-- 插入测试操作员用户 (密码: operator123)
INSERT OR REPLACE INTO users (username, password_hash, email, role, is_active)
VALUES ('operator', '$2a$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'operator', 1);

-- 创建触发器以自动更新 updated_at 字段
CREATE TRIGGER IF NOT EXISTS update_users_timestamp 
    AFTER UPDATE ON users
    BEGIN
        UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_beacons_timestamp 
    AFTER UPDATE ON beacons
    BEGIN
        UPDATE beacons SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
