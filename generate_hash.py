#!/usr/bin/env python3
import bcrypt

def generate_hash(password):
    # 生成bcrypt哈希
    salt = bcrypt.gensalt(rounds=12)
    hash_bytes = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hash_bytes.decode('utf-8')

# 生成admin123的哈希
admin_hash = generate_hash("admin123")
print(f"admin123 hash: {admin_hash}")

# 生成operator123的哈希
operator_hash = generate_hash("operator123")
print(f"operator123 hash: {operator_hash}")

# 验证哈希
print("\n验证:")
print(f"admin123 验证: {bcrypt.checkpw(b'admin123', admin_hash.encode('utf-8'))}")
print(f"operator123 验证: {bcrypt.checkpw(b'operator123', operator_hash.encode('utf-8'))}")
