# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Database files
*.db
*.sqlite
*.sqlite3

# Log files
logs/
*.log

# Configuration files with sensitive data
config.local.yaml
.env
.env.local

# Upload directories
uploads/
temp/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Compiled Beacon files
beacons/c/*.exe
beacons/c/*.o
beacons/csharp/bin/
beacons/csharp/obj/
beacons/csharp/*.exe

# Node modules (if using npm for frontend dependencies)
node_modules/
package-lock.json
yarn.lock

# Backup files
*.bak
*.backup

# Temporary files
*.tmp
*.temp
