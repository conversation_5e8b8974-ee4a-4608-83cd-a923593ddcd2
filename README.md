# GateSentinel - 准入控制与Beacon管理系统

GateSentinel是一个高度模块化的红队C2系统，提供准入控制与Beacon通信管理功能。

## 系统架构

```
GateSentinel/
├── backend/           # Golang后端服务
├── frontend/          # Web前端界面
├── beacons/           # Beacon客户端实现
│   ├── c/            # C语言版本Beacon
│   └── csharp/       # C#版本Beacon
├── db/               # 数据库相关文件
├── static/           # 静态资源文件
│   ├── css/         # 样式文件
│   ├── js/          # JavaScript文件
│   └── images/      # 图片资源
└── docs/            # 项目文档
```

## 核心功能

1. **用户认证与准入控制**
   - 安全的用户登录系统
   - 基于角色的权限验证

2. **Beacon管理**
   - HTTP/HTTPS Beacon注册与上线
   - 实时状态监控（在线/离线状态）
   - 系统信息收集与展示

3. **命令执行**
   - 远程命令下发与执行
   - 实时结果回传与展示
   - 命令历史记录

4. **Shellcode管理**
   - Shellcode上传与存储
   - 选择性下发到指定Beacon
   - 执行状态跟踪

5. **通信安全**
   - JSON格式数据交换
   - XOR/Base64加密混淆
   - 可配置回传频率

## 技术栈

- **后端**: Golang + Gin框架
- **前端**: HTML + CSS + JavaScript (原生/Alpine.js)
- **数据库**: SQLite
- **Beacon**: C/C#双版本实现

## 快速开始

### 环境要求

- Go 1.19+
- GCC (用于编译C版本Beacon)
- .NET SDK (用于编译C#版本Beacon)

### 安装与运行

1. 克隆项目
```bash
git clone <repository-url>
cd GateSentinel
```

2. 初始化数据库
```bash
cd db
sqlite3 gatesentinel.db < init.sql
```

3. 启动后端服务
```bash
cd backend
go mod tidy
go run main.go
```

4. 访问Web界面
```
http://localhost:8080
```

## 安全声明

⚠️ **重要提醒**: 本项目仅用于授权的安全测试和教育目的。使用者必须确保在合法合规的环境中使用本系统，并承担相应的法律责任。

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请通过Issue联系我们。
