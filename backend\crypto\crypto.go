package crypto

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
)

// EncryptionMethod 加密方法枚举
type EncryptionMethod string

const (
	MethodNone   EncryptionMethod = "none"
	MethodXOR    EncryptionMethod = "xor"
	MethodBase64 EncryptionMethod = "base64"
	MethodAES    EncryptionMethod = "aes"
)

// Encryptor 加密器接口
type Encryptor interface {
	Encrypt(data []byte) ([]byte, error)
	Decrypt(data []byte) ([]byte, error)
}

// XOREncryptor XOR加密器
type XOREncryptor struct {
	key []byte
}

// NewXOREncryptor 创建XOR加密器
func NewXOREncryptor(key string) *XOREncryptor {
	return &XOREncryptor{
		key: []byte(key),
	}
}

// Encrypt XOR加密
func (x *XOREncryptor) Encrypt(data []byte) ([]byte, error) {
	if len(x.key) == 0 {
		return data, nil
	}

	result := make([]byte, len(data))
	for i, b := range data {
		result[i] = b ^ x.key[i%len(x.key)]
	}
	return result, nil
}

// Decrypt XOR解密
func (x *XOREncryptor) Decrypt(data []byte) ([]byte, error) {
	// XOR加密和解密是相同的操作
	return x.Encrypt(data)
}

// Base64Encryptor Base64编码器
type Base64Encryptor struct{}

// NewBase64Encryptor 创建Base64编码器
func NewBase64Encryptor() *Base64Encryptor {
	return &Base64Encryptor{}
}

// Encrypt Base64编码
func (b *Base64Encryptor) Encrypt(data []byte) ([]byte, error) {
	encoded := base64.StdEncoding.EncodeToString(data)
	return []byte(encoded), nil
}

// Decrypt Base64解码
func (b *Base64Encryptor) Decrypt(data []byte) ([]byte, error) {
	decoded, err := base64.StdEncoding.DecodeString(string(data))
	if err != nil {
		return nil, fmt.Errorf("Base64解码失败: %v", err)
	}
	return decoded, nil
}

// AESEncryptor AES加密器
type AESEncryptor struct {
	key []byte
}

// NewAESEncryptor 创建AES加密器
func NewAESEncryptor(key string) (*AESEncryptor, error) {
	// 确保密钥长度为16、24或32字节
	keyBytes := []byte(key)
	switch len(keyBytes) {
	case 16, 24, 32:
		// 密钥长度正确
	default:
		// 调整密钥长度到32字节
		if len(keyBytes) < 32 {
			// 填充到32字节
			newKey := make([]byte, 32)
			copy(newKey, keyBytes)
			keyBytes = newKey
		} else {
			// 截断到32字节
			keyBytes = keyBytes[:32]
		}
	}

	return &AESEncryptor{
		key: keyBytes,
	}, nil
}

// Encrypt AES加密
func (a *AESEncryptor) Encrypt(data []byte) ([]byte, error) {
	block, err := aes.NewCipher(a.key)
	if err != nil {
		return nil, fmt.Errorf("创建AES密码器失败: %v", err)
	}

	// 使用GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("创建GCM失败: %v", err)
	}

	// 生成随机nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, fmt.Errorf("生成nonce失败: %v", err)
	}

	// 加密数据
	ciphertext := gcm.Seal(nonce, nonce, data, nil)
	return ciphertext, nil
}

// Decrypt AES解密
func (a *AESEncryptor) Decrypt(data []byte) ([]byte, error) {
	block, err := aes.NewCipher(a.key)
	if err != nil {
		return nil, fmt.Errorf("创建AES密码器失败: %v", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("创建GCM失败: %v", err)
	}

	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return nil, fmt.Errorf("密文长度不足")
	}

	nonce, ciphertext := data[:nonceSize], data[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("AES解密失败: %v", err)
	}

	return plaintext, nil
}

// NoneEncryptor 无加密器
type NoneEncryptor struct{}

// NewNoneEncryptor 创建无加密器
func NewNoneEncryptor() *NoneEncryptor {
	return &NoneEncryptor{}
}

// Encrypt 不加密
func (n *NoneEncryptor) Encrypt(data []byte) ([]byte, error) {
	return data, nil
}

// Decrypt 不解密
func (n *NoneEncryptor) Decrypt(data []byte) ([]byte, error) {
	return data, nil
}

// CreateEncryptor 根据配置创建加密器
func CreateEncryptor(method EncryptionMethod, key string) (Encryptor, error) {
	switch method {
	case MethodNone:
		return NewNoneEncryptor(), nil
	case MethodXOR:
		return NewXOREncryptor(key), nil
	case MethodBase64:
		return NewBase64Encryptor(), nil
	case MethodAES:
		return NewAESEncryptor(key)
	default:
		return nil, fmt.Errorf("不支持的加密方法: %s", method)
	}
}

// EncryptString 加密字符串
func EncryptString(encryptor Encryptor, data string) (string, error) {
	encrypted, err := encryptor.Encrypt([]byte(data))
	if err != nil {
		return "", err
	}
	return string(encrypted), nil
}

// DecryptString 解密字符串
func DecryptString(encryptor Encryptor, data string) (string, error) {
	decrypted, err := encryptor.Decrypt([]byte(data))
	if err != nil {
		return "", err
	}
	return string(decrypted), nil
}

// EncryptJSON 加密JSON数据
func EncryptJSON(encryptor Encryptor, jsonData string) (string, error) {
	encrypted, err := encryptor.Encrypt([]byte(jsonData))
	if err != nil {
		return "", err
	}
	
	// 对加密后的数据进行Base64编码以便传输
	encoded := base64.StdEncoding.EncodeToString(encrypted)
	return encoded, nil
}

// DecryptJSON 解密JSON数据
func DecryptJSON(encryptor Encryptor, encodedData string) (string, error) {
	// 先进行Base64解码
	encrypted, err := base64.StdEncoding.DecodeString(encodedData)
	if err != nil {
		return "", fmt.Errorf("Base64解码失败: %v", err)
	}
	
	// 然后解密
	decrypted, err := encryptor.Decrypt(encrypted)
	if err != nil {
		return "", err
	}
	
	return string(decrypted), nil
}
