<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - GateSentinel</title>
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h1>GateSentinel</h1>
                <p>准入控制与Beacon管理系统</p>
            </div>
            
            <form id="loginForm">
                <div class="form-group">
                    <label for="username" class="form-label">用户名</label>
                    <input type="text" id="username" name="username" class="form-input" required>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">密码</label>
                    <input type="password" id="password" name="password" class="form-input" required>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary" style="width: 100%;">
                        <span id="loginText">登录</span>
                        <span id="loginLoading" class="loading" style="display: none;"></span>
                    </button>
                </div>
            </form>
            
            <div id="errorMessage" class="alert alert-danger" style="display: none;"></div>
            
            <div style="margin-top: 2rem; padding-top: 1rem; border-top: 1px solid #eee; font-size: 0.875rem; color: #666; text-align: center;">
                <p>默认账户：</p>
                <p>管理员 - admin / admin123</p>
                <p>操作员 - operator / operator123</p>
            </div>
        </div>
    </div>

    <script src="/static/js/utils.js"></script>
    <script>
        // 如果已经登录，跳转到主页
        if (isLoggedIn()) {
            window.location.href = '/';
        }

        // 登录表单处理
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('errorMessage');
            const loginText = document.getElementById('loginText');
            const loginLoading = document.getElementById('loginLoading');
            
            // 隐藏错误消息
            errorDiv.style.display = 'none';
            
            // 显示加载状态
            loginText.style.display = 'none';
            loginLoading.style.display = 'inline-block';
            
            try {
                const response = await fetch('/api/v1/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // 登录成功，保存令牌和用户信息
                    setToken(data.token);
                    setUser(data.user);
                    
                    // 跳转到主页
                    window.location.href = '/';
                } else {
                    // 显示错误消息
                    errorDiv.textContent = data.error || '登录失败';
                    errorDiv.style.display = 'block';
                }
            } catch (error) {
                console.error('登录错误:', error);
                errorDiv.textContent = '网络错误，请稍后重试';
                errorDiv.style.display = 'block';
            } finally {
                // 恢复按钮状态
                loginText.style.display = 'inline';
                loginLoading.style.display = 'none';
            }
        });

        // 回车键登录
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });

        // 演示账户快速登录
        function quickLogin(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            document.getElementById('loginForm').dispatchEvent(new Event('submit'));
        }

        // 添加快速登录按钮点击事件
        document.addEventListener('click', (e) => {
            if (e.target.textContent.includes('admin / admin123')) {
                quickLogin('admin', 'admin123');
            } else if (e.target.textContent.includes('operator / operator123')) {
                quickLogin('operator', 'operator123');
            }
        });
    </script>
</body>
</html>
