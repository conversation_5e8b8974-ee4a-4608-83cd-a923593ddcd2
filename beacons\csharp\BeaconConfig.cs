using System;

namespace GateSentinel.Beacon
{
    public class BeaconConfig
    {
        public string BeaconId { get; set; } = string.Empty;
        public string ServerUrl { get; set; } = "http://127.0.0.1:8080";
        public int SleepTime { get; set; } = 60; // 默认60秒
        public int Jitter { get; set; } = 10; // 默认10%抖动
        public int MaxRetries { get; set; } = 3;
        public bool IsRegistered { get; set; } = false;

        public BeaconConfig()
        {
            // 从环境变量或配置文件读取配置（如果存在）
            LoadFromEnvironment();
        }

        private void LoadFromEnvironment()
        {
            // 从环境变量读取服务器URL
            var serverUrl = Environment.GetEnvironmentVariable("BEACON_SERVER_URL");
            if (!string.IsNullOrEmpty(serverUrl))
            {
                ServerUrl = serverUrl;
            }

            // 从环境变量读取睡眠时间
            var sleepTimeStr = Environment.GetEnvironmentVariable("BEACON_SLEEP_TIME");
            if (!string.IsNullOrEmpty(sleepTimeStr) && int.TryParse(sleepTimeStr, out int sleepTime))
            {
                SleepTime = Math.Max(sleepTime, 1); // 最小1秒
            }

            // 从环境变量读取抖动
            var jitterStr = Environment.GetEnvironmentVariable("BEACON_JITTER");
            if (!string.IsNullOrEmpty(jitterStr) && int.TryParse(jitterStr, out int jitter))
            {
                Jitter = Math.Max(0, Math.Min(jitter, 100)); // 0-100%
            }
        }

        public void UpdateFromServer(int sleepTime, int jitter)
        {
            if (sleepTime > 0)
            {
                SleepTime = sleepTime;
            }

            if (jitter >= 0 && jitter <= 100)
            {
                Jitter = jitter;
            }
        }

        public override string ToString()
        {
            return $"BeaconConfig {{ BeaconId: {BeaconId}, ServerUrl: {ServerUrl}, SleepTime: {SleepTime}, Jitter: {Jitter}% }}";
        }
    }
}
