#include "http.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#ifdef _WIN32
    #include <windows.h>
    #include <wininet.h>
    #pragma comment(lib, "wininet.lib")
#else
    #include <curl/curl.h>
#endif

// HTTP响应写入回调函数
#ifndef _WIN32
static size_t WriteCallback(void *contents, size_t size, size_t nmemb, HTTPResponse *response) {
    size_t realsize = size * nmemb;
    char *ptr = realloc(response->data, response->size + realsize + 1);
    
    if (!ptr) {
        printf("[ERROR] 内存分配失败\n");
        return 0;
    }
    
    response->data = ptr;
    memcpy(&(response->data[response->size]), contents, realsize);
    response->size += realsize;
    response->data[response->size] = 0;
    
    return realsize;
}
#endif

// 发送HTTP GET请求
int http_get(const char *url, HTTPResponse *response) {
    if (!url || !response) return -1;
    
    response->data = NULL;
    response->size = 0;
    
#ifdef _WIN32
    HINTERNET hInternet, hConnect;
    DWORD bytesRead;
    char buffer[4096];
    
    // 初始化WinINet
    hInternet = InternetOpenA("GateSentinel-Beacon/1.0", 
                             INTERNET_OPEN_TYPE_DIRECT, NULL, NULL, 0);
    if (!hInternet) {
        return -1;
    }
    
    // 打开URL
    hConnect = InternetOpenUrlA(hInternet, url, NULL, 0, 
                               INTERNET_FLAG_RELOAD | INTERNET_FLAG_NO_CACHE_WRITE, 0);
    if (!hConnect) {
        InternetCloseHandle(hInternet);
        return -1;
    }
    
    // 读取响应数据
    while (InternetReadFile(hConnect, buffer, sizeof(buffer) - 1, &bytesRead) && bytesRead > 0) {
        buffer[bytesRead] = '\0';
        
        char *newData = realloc(response->data, response->size + bytesRead + 1);
        if (!newData) {
            free(response->data);
            InternetCloseHandle(hConnect);
            InternetCloseHandle(hInternet);
            return -1;
        }
        
        response->data = newData;
        if (response->size == 0) {
            response->data[0] = '\0';
        }
        strcat(response->data, buffer);
        response->size += bytesRead;
    }
    
    InternetCloseHandle(hConnect);
    InternetCloseHandle(hInternet);
    
    return 0;
    
#else
    CURL *curl;
    CURLcode res;
    
    curl = curl_easy_init();
    if (!curl) {
        return -1;
    }
    
    curl_easy_setopt(curl, CURLOPT_URL, url);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
    curl_easy_setopt(curl, CURLOPT_USERAGENT, "GateSentinel-Beacon/1.0");
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 30L);
    curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
    
    res = curl_easy_perform(curl);
    curl_easy_cleanup(curl);
    
    return (res == CURLE_OK) ? 0 : -1;
#endif
}

// 发送HTTP POST请求
int http_post(const char *url, const char *data, HTTPResponse *response) {
    if (!url || !data || !response) return -1;
    
    response->data = NULL;
    response->size = 0;
    
#ifdef _WIN32
    HINTERNET hInternet, hConnect, hRequest;
    DWORD bytesRead;
    char buffer[4096];
    
    // 解析URL
    char hostname[256] = {0};
    char path[512] = {0};
    int port = 80;
    int isHttps = 0;
    
    // 简化的URL解析
    if (strncmp(url, "https://", 8) == 0) {
        isHttps = 1;
        port = 443;
        sscanf(url + 8, "%255[^/]%511s", hostname, path);
    } else if (strncmp(url, "http://", 7) == 0) {
        sscanf(url + 7, "%255[^/]%511s", hostname, path);
    } else {
        return -1;
    }
    
    // 检查端口
    char *portPos = strchr(hostname, ':');
    if (portPos) {
        *portPos = '\0';
        port = atoi(portPos + 1);
    }
    
    if (strlen(path) == 0) {
        strcpy(path, "/");
    }
    
    // 初始化WinINet
    hInternet = InternetOpenA("GateSentinel-Beacon/1.0", 
                             INTERNET_OPEN_TYPE_DIRECT, NULL, NULL, 0);
    if (!hInternet) {
        return -1;
    }
    
    // 连接到服务器
    hConnect = InternetConnectA(hInternet, hostname, port, NULL, NULL, 
                               INTERNET_SERVICE_HTTP, 0, 0);
    if (!hConnect) {
        InternetCloseHandle(hInternet);
        return -1;
    }
    
    // 创建HTTP请求
    DWORD flags = INTERNET_FLAG_RELOAD | INTERNET_FLAG_NO_CACHE_WRITE;
    if (isHttps) {
        flags |= INTERNET_FLAG_SECURE;
    }
    
    hRequest = HttpOpenRequestA(hConnect, "POST", path, NULL, NULL, NULL, flags, 0);
    if (!hRequest) {
        InternetCloseHandle(hConnect);
        InternetCloseHandle(hInternet);
        return -1;
    }
    
    // 设置请求头
    const char *headers = "Content-Type: application/json\r\n";
    
    // 发送请求
    BOOL result = HttpSendRequestA(hRequest, headers, strlen(headers), 
                                  (LPVOID)data, strlen(data));
    if (!result) {
        InternetCloseHandle(hRequest);
        InternetCloseHandle(hConnect);
        InternetCloseHandle(hInternet);
        return -1;
    }
    
    // 读取响应
    while (InternetReadFile(hRequest, buffer, sizeof(buffer) - 1, &bytesRead) && bytesRead > 0) {
        buffer[bytesRead] = '\0';
        
        char *newData = realloc(response->data, response->size + bytesRead + 1);
        if (!newData) {
            free(response->data);
            InternetCloseHandle(hRequest);
            InternetCloseHandle(hConnect);
            InternetCloseHandle(hInternet);
            return -1;
        }
        
        response->data = newData;
        if (response->size == 0) {
            response->data[0] = '\0';
        }
        strcat(response->data, buffer);
        response->size += bytesRead;
    }
    
    InternetCloseHandle(hRequest);
    InternetCloseHandle(hConnect);
    InternetCloseHandle(hInternet);
    
    return 0;
    
#else
    CURL *curl;
    CURLcode res;
    struct curl_slist *headers = NULL;
    
    curl = curl_easy_init();
    if (!curl) {
        return -1;
    }
    
    // 设置请求头
    headers = curl_slist_append(headers, "Content-Type: application/json");
    
    curl_easy_setopt(curl, CURLOPT_URL, url);
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data);
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
    curl_easy_setopt(curl, CURLOPT_USERAGENT, "GateSentinel-Beacon/1.0");
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 30L);
    curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
    
    res = curl_easy_perform(curl);
    
    curl_slist_free_all(headers);
    curl_easy_cleanup(curl);
    
    return (res == CURLE_OK) ? 0 : -1;
#endif
}

// 释放HTTP响应数据
void http_free_response(HTTPResponse *response) {
    if (response && response->data) {
        free(response->data);
        response->data = NULL;
        response->size = 0;
    }
}
