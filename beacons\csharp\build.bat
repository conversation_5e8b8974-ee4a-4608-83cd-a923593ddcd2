@echo off
echo Building GateSentinel C# Beacon...

REM 检查.NET SDK
dotnet --version >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Error: .NET SDK not found. Please install .NET 6.0 or later.
    pause
    exit /b 1
)

REM 清理之前的构建
echo Cleaning previous builds...
dotnet clean --configuration Release --verbosity quiet

REM 构建项目
echo Building project...
dotnet build --configuration Release --verbosity quiet

if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

REM 发布单文件可执行程序
echo Publishing single-file executable...

REM Windows x64版本
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output bin\win-x64 /p:PublishSingleFile=true /p:PublishTrimmed=true --verbosity quiet

REM Windows x86版本
dotnet publish --configuration Release --runtime win-x86 --self-contained true --output bin\win-x86 /p:PublishSingleFile=true /p:PublishTrimmed=true --verbosity quiet

REM Linux x64版本
dotnet publish --configuration Release --runtime linux-x64 --self-contained true --output bin\linux-x64 /p:PublishSingleFile=true /p:PublishTrimmed=true --verbosity quiet

if %ERRORLEVEL% EQU 0 (
    echo Build successful!
    echo.
    echo Output files:
    echo   Windows x64: bin\win-x64\Beacon.exe
    echo   Windows x86: bin\win-x86\Beacon.exe
    echo   Linux x64:   bin\linux-x64\Beacon
    echo.
    echo Usage: 
    echo   Windows: bin\win-x64\Beacon.exe
    echo   Linux:   ./bin/linux-x64/Beacon
    echo.
    echo Environment variables (optional):
    echo   BEACON_SERVER_URL=http://your-server:8080
    echo   BEACON_SLEEP_TIME=60
    echo   BEACON_JITTER=10
    echo.
) else (
    echo Publish failed!
    pause
    exit /b 1
)

echo Done.
pause
