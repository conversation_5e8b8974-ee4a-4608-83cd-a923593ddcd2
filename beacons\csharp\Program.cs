using System;
using System.Threading.Tasks;

namespace GateSentinel.Beacon
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("GateSentinel Beacon v1.0 (C#版本)");
            Console.WriteLine("==================================");

            try
            {
                // 创建Beacon实例
                var beacon = new BeaconClient();
                
                // 初始化配置
                await beacon.InitializeAsync();
                
                // 开始运行
                await beacon.RunAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] Beacon运行失败: {ex.Message}");
                Console.WriteLine($"[DEBUG] 详细错误: {ex}");
                
                if (System.Diagnostics.Debugger.IsAttached)
                {
                    Console.WriteLine("按任意键退出...");
                    Console.ReadKey();
                }
                
                Environment.Exit(1);
            }
        }
    }
}
