package handlers

import (
	"net/http"
	"strings"

	"gatesentinel/database"
	"gatesentinel/middleware"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
)

// LoginRequest 登录请求结构
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应结构
type LoginResponse struct {
	Token    string         `json:"token"`
	User     *database.User `json:"user"`
	ExpiresIn int           `json:"expires_in"`
}

// ChangePasswordRequest 修改密码请求结构
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=6"`
}

// Login 用户登录
func Login(c *gin.Context) {
	var req LoginRequest
	if err := c.Should<PERSON>indJ<PERSON>(&req); err != nil {
		c.<PERSON><PERSON>(http.StatusBadRequest, gin.H{
			"error": "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	// 查找用户
	user, err := database.GetUserByUsername(req.Username)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "用户名或密码错误",
		})
		return
	}

	// 验证密码
	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "用户名或密码错误",
		})
		return
	}

	// 生成JWT令牌
	token, err := middleware.GenerateToken(user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "生成令牌失败",
		})
		return
	}

	// 更新最后登录时间
	database.UpdateUserLastLogin(user.ID)

	// 记录登录日志
	// LogSystemEvent("INFO", "AUTH", "用户登录成功", user.ID, "", c.ClientIP(), c.GetHeader("User-Agent"))

	// 返回响应
	c.JSON(http.StatusOK, LoginResponse{
		Token:     token,
		User:      user,
		ExpiresIn: 24 * 3600, // 24小时，单位秒
	})
}

// GetProfile 获取用户资料
func GetProfile(c *gin.Context) {
	userID, _, _ := middleware.GetCurrentUser(c)

	user, err := database.GetUserByID(userID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "用户不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"user": user,
	})
}

// ChangePassword 修改密码
func ChangePassword(c *gin.Context) {
	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	userID, _, _ := middleware.GetCurrentUser(c)

	// 获取当前用户信息
	user, err := database.GetUserByID(userID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "用户不存在",
		})
		return
	}

	// 验证旧密码
	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.OldPassword))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "旧密码错误",
		})
		return
	}

	// 生成新密码哈希
	newPasswordHash, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "密码加密失败",
		})
		return
	}

	// 更新密码
	err = database.UpdateUserPassword(userID, string(newPasswordHash))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "更新密码失败",
		})
		return
	}

	// 记录日志
	// LogSystemEvent("INFO", "AUTH", "用户修改密码", userID, "", c.ClientIP(), c.GetHeader("User-Agent"))

	c.JSON(http.StatusOK, gin.H{
		"message": "密码修改成功",
	})
}

// Logout 用户登出
func Logout(c *gin.Context) {
	_, username, _ := middleware.GetCurrentUser(c)

	// 记录登出日志
	// LogSystemEvent("INFO", "AUTH", "用户登出", userID, "", c.ClientIP(), c.GetHeader("User-Agent"))

	c.JSON(http.StatusOK, gin.H{
		"message": "登出成功",
		"username": username,
	})
}

// ValidateToken 验证令牌
func ValidateToken(c *gin.Context) {
	// 从请求头获取令牌
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"valid": false,
			"error": "缺少认证令牌",
		})
		return
	}

	// 检查Bearer前缀
	tokenParts := strings.SplitN(authHeader, " ", 2)
	if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"valid": false,
			"error": "无效的令牌格式",
		})
		return
	}

	// 验证令牌
	claims, err := middleware.ValidateToken(tokenParts[1])
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"valid": false,
			"error": "令牌验证失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"valid": true,
		"user": gin.H{
			"id":       claims.UserID,
			"username": claims.Username,
			"role":     claims.Role,
		},
	})
}
