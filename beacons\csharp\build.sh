#!/bin/bash

echo "Building GateSentinel C# Beacon..."

# 检查.NET SDK
if ! command -v dotnet &> /dev/null; then
    echo "Error: .NET SDK not found. Please install .NET 6.0 or later."
    echo "Visit: https://dotnet.microsoft.com/download"
    exit 1
fi

echo "Using .NET version: $(dotnet --version)"

# 清理之前的构建
echo "Cleaning previous builds..."
dotnet clean --configuration Release --verbosity quiet

# 构建项目
echo "Building project..."
dotnet build --configuration Release --verbosity quiet

if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

# 发布单文件可执行程序
echo "Publishing single-file executable..."

# Linux x64版本
dotnet publish --configuration Release --runtime linux-x64 --self-contained true --output bin/linux-x64 /p:PublishSingleFile=true /p:PublishTrimmed=true --verbosity quiet

# Linux ARM64版本（适用于ARM处理器）
dotnet publish --configuration Release --runtime linux-arm64 --self-contained true --output bin/linux-arm64 /p:PublishSingleFile=true /p:PublishTrimmed=true --verbosity quiet

# Windows x64版本（交叉编译）
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output bin/win-x64 /p:PublishSingleFile=true /p:PublishTrimmed=true --verbosity quiet

if [ $? -eq 0 ]; then
    echo "Build successful!"
    echo ""
    echo "Output files:"
    echo "  Linux x64:    bin/linux-x64/Beacon"
    echo "  Linux ARM64:  bin/linux-arm64/Beacon"
    echo "  Windows x64:  bin/win-x64/Beacon.exe"
    echo ""
    
    # 设置执行权限
    chmod +x bin/linux-x64/Beacon 2>/dev/null
    chmod +x bin/linux-arm64/Beacon 2>/dev/null
    
    echo "Usage:"
    echo "  Linux:   ./bin/linux-x64/Beacon"
    echo "  Windows: bin/win-x64/Beacon.exe"
    echo ""
    echo "Environment variables (optional):"
    echo "  export BEACON_SERVER_URL=http://your-server:8080"
    echo "  export BEACON_SLEEP_TIME=60"
    echo "  export BEACON_JITTER=10"
    echo ""
else
    echo "Publish failed!"
    exit 1
fi

echo "Done."
