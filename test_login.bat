@echo off
echo ========================================
echo   GateSentinel 登录测试
echo ========================================
echo.

set SERVER_URL=http://localhost:8080

echo [INFO] 测试服务器连接...
curl -s -o nul -w "HTTP状态码: %%{http_code}" %SERVER_URL%/health
echo.

echo [INFO] 测试admin用户登录...
curl -s -X POST -H "Content-Type: application/json" ^
     -d "{\"username\":\"admin\",\"password\":\"admin123\"}" ^
     %SERVER_URL%/api/v1/login

echo.
echo.

echo [INFO] 测试operator用户登录...
curl -s -X POST -H "Content-Type: application/json" ^
     -d "{\"username\":\"operator\",\"password\":\"operator123\"}" ^
     %SERVER_URL%/api/v1/login

echo.
echo.

echo [INFO] 测试错误密码...
curl -s -X POST -H "Content-Type: application/json" ^
     -d "{\"username\":\"admin\",\"password\":\"wrongpassword\"}" ^
     %SERVER_URL%/api/v1/login

echo.
echo.
echo 测试完成！
pause
