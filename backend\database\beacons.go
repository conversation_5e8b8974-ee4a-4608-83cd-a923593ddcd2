package database

import (
	"database/sql"
	"fmt"
	"time"
)

// Beacon Beacon结构体
type Beacon struct {
	ID          int       `json:"id"`
	BeaconID    string    `json:"beacon_id"`
	Hostname    string    `json:"hostname"`
	Username    string    `json:"username"`
	OSInfo      string    `json:"os_info"`
	Arch        string    `json:"arch"`
	ProcessName string    `json:"process_name"`
	ProcessID   int       `json:"process_id"`
	IPAddress   string    `json:"ip_address"`
	ExternalIP  string    `json:"external_ip"`
	SleepTime   int       `json:"sleep_time"`
	Jitter      int       `json:"jitter"`
	IsOnline    bool      `json:"is_online"`
	FirstSeen   time.Time `json:"first_seen"`
	LastSeen    time.Time `json:"last_seen"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// CreateBeacon 创建新的Beacon
func CreateBeacon(beacon *Beacon) error {
	query := `
		INSERT INTO beacons (
			beacon_id, hostname, username, os_info, arch, process_name, 
			process_id, ip_address, external_ip, sleep_time, jitter
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`
	result, err := DB.Exec(query,
		beacon.BeaconID, beacon.Hostname, beacon.Username, beacon.OSInfo,
		beacon.Arch, beacon.ProcessName, beacon.ProcessID, beacon.IPAddress,
		beacon.ExternalIP, beacon.SleepTime, beacon.Jitter,
	)
	if err != nil {
		return fmt.Errorf("创建Beacon失败: %v", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取Beacon ID失败: %v", err)
	}

	beacon.ID = int(id)
	return nil
}

// GetBeaconByID 根据BeaconID获取Beacon
func GetBeaconByID(beaconID string) (*Beacon, error) {
	beacon := &Beacon{}
	query := `
		SELECT id, beacon_id, hostname, username, os_info, arch, process_name,
		       process_id, ip_address, external_ip, sleep_time, jitter, is_online,
		       first_seen, last_seen, created_at, updated_at
		FROM beacons 
		WHERE beacon_id = ?
	`
	
	err := DB.QueryRow(query, beaconID).Scan(
		&beacon.ID, &beacon.BeaconID, &beacon.Hostname, &beacon.Username,
		&beacon.OSInfo, &beacon.Arch, &beacon.ProcessName, &beacon.ProcessID,
		&beacon.IPAddress, &beacon.ExternalIP, &beacon.SleepTime, &beacon.Jitter,
		&beacon.IsOnline, &beacon.FirstSeen, &beacon.LastSeen,
		&beacon.CreatedAt, &beacon.UpdatedAt,
	)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("Beacon不存在")
		}
		return nil, fmt.Errorf("查询Beacon失败: %v", err)
	}

	return beacon, nil
}

// UpdateBeaconHeartbeat 更新Beacon心跳时间
func UpdateBeaconHeartbeat(beaconID string) error {
	query := `
		UPDATE beacons 
		SET last_seen = CURRENT_TIMESTAMP, is_online = 1 
		WHERE beacon_id = ?
	`
	_, err := DB.Exec(query, beaconID)
	if err != nil {
		return fmt.Errorf("更新Beacon心跳失败: %v", err)
	}
	return nil
}

// GetAllBeacons 获取所有Beacon列表
func GetAllBeacons() ([]*Beacon, error) {
	query := `
		SELECT id, beacon_id, hostname, username, os_info, arch, process_name,
		       process_id, ip_address, external_ip, sleep_time, jitter, is_online,
		       first_seen, last_seen, created_at, updated_at
		FROM beacons 
		ORDER BY last_seen DESC
	`
	
	rows, err := DB.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询Beacon列表失败: %v", err)
	}
	defer rows.Close()

	var beacons []*Beacon
	for rows.Next() {
		beacon := &Beacon{}
		
		err := rows.Scan(
			&beacon.ID, &beacon.BeaconID, &beacon.Hostname, &beacon.Username,
			&beacon.OSInfo, &beacon.Arch, &beacon.ProcessName, &beacon.ProcessID,
			&beacon.IPAddress, &beacon.ExternalIP, &beacon.SleepTime, &beacon.Jitter,
			&beacon.IsOnline, &beacon.FirstSeen, &beacon.LastSeen,
			&beacon.CreatedAt, &beacon.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描Beacon数据失败: %v", err)
		}

		beacons = append(beacons, beacon)
	}

	return beacons, nil
}

// GetOnlineBeacons 获取在线的Beacon列表
func GetOnlineBeacons() ([]*Beacon, error) {
	query := `
		SELECT id, beacon_id, hostname, username, os_info, arch, process_name,
		       process_id, ip_address, external_ip, sleep_time, jitter, is_online,
		       first_seen, last_seen, created_at, updated_at
		FROM beacons 
		WHERE is_online = 1
		ORDER BY last_seen DESC
	`
	
	rows, err := DB.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询在线Beacon列表失败: %v", err)
	}
	defer rows.Close()

	var beacons []*Beacon
	for rows.Next() {
		beacon := &Beacon{}
		
		err := rows.Scan(
			&beacon.ID, &beacon.BeaconID, &beacon.Hostname, &beacon.Username,
			&beacon.OSInfo, &beacon.Arch, &beacon.ProcessName, &beacon.ProcessID,
			&beacon.IPAddress, &beacon.ExternalIP, &beacon.SleepTime, &beacon.Jitter,
			&beacon.IsOnline, &beacon.FirstSeen, &beacon.LastSeen,
			&beacon.CreatedAt, &beacon.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描Beacon数据失败: %v", err)
		}

		beacons = append(beacons, beacon)
	}

	return beacons, nil
}

// UpdateBeaconConfig 更新Beacon配置
func UpdateBeaconConfig(beaconID string, sleepTime, jitter int) error {
	query := `
		UPDATE beacons 
		SET sleep_time = ?, jitter = ?
		WHERE beacon_id = ?
	`
	_, err := DB.Exec(query, sleepTime, jitter, beaconID)
	if err != nil {
		return fmt.Errorf("更新Beacon配置失败: %v", err)
	}
	return nil
}

// MarkBeaconOffline 标记Beacon为离线状态
func MarkBeaconOffline(beaconID string) error {
	query := `UPDATE beacons SET is_online = 0 WHERE beacon_id = ?`
	_, err := DB.Exec(query, beaconID)
	if err != nil {
		return fmt.Errorf("标记Beacon离线失败: %v", err)
	}
	return nil
}

// DeleteBeacon 删除Beacon
func DeleteBeacon(beaconID string) error {
	return WithTransaction(func(tx *sql.Tx) error {
		// 删除相关的命令记录
		_, err := tx.Exec("DELETE FROM command_results WHERE command_id IN (SELECT id FROM commands WHERE beacon_id = ?)", beaconID)
		if err != nil {
			return fmt.Errorf("删除命令结果失败: %v", err)
		}

		_, err = tx.Exec("DELETE FROM commands WHERE beacon_id = ?", beaconID)
		if err != nil {
			return fmt.Errorf("删除命令记录失败: %v", err)
		}

		// 删除Beacon
		_, err = tx.Exec("DELETE FROM beacons WHERE beacon_id = ?", beaconID)
		if err != nil {
			return fmt.Errorf("删除Beacon失败: %v", err)
		}

		return nil
	})
}

// CheckBeaconTimeout 检查并标记超时的Beacon为离线
func CheckBeaconTimeout(timeoutSeconds int) error {
	query := `
		UPDATE beacons 
		SET is_online = 0 
		WHERE is_online = 1 
		AND datetime(last_seen, '+' || ? || ' seconds') < datetime('now')
	`
	_, err := DB.Exec(query, timeoutSeconds)
	if err != nil {
		return fmt.Errorf("检查Beacon超时失败: %v", err)
	}
	return nil
}
