# GateSentinel 配置文件

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  debug: true
  
# 数据库配置
database:
  type: "sqlite"
  path: "../db/gatesentinel.db"
  
# 安全配置
security:
  jwt_secret: "your-super-secret-jwt-key-change-this-in-production"
  jwt_expire_hours: 24
  bcrypt_cost: 12
  
# Beacon配置
beacon:
  default_sleep: 60  # 默认心跳间隔（秒）
  max_sleep: 3600    # 最大心跳间隔（秒）
  timeout: 300       # Beacon超时时间（秒）
  
# 加密配置
encryption:
  enabled: true
  method: "xor"      # 支持: xor, base64, aes
  key: "DefaultXORKey123"
  
# 日志配置
logging:
  level: "info"      # debug, info, warn, error
  file: "./logs/gatesentinel.log"
  max_size: 100      # MB
  max_backups: 5
  max_age: 30        # 天
  
# Web界面配置
web:
  static_path: "../static"
  template_path: "../frontend"
  upload_path: "./uploads"
  max_upload_size: 10  # MB
